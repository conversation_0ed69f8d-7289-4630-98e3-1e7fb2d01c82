import ScrollRevealSection from "@/components/ScrollReveal";
import { Badge } from "@/components/ui/badge";
import { Mail, Phone, Clock, PhoneCall, MailIcon } from "lucide-react";
import { getTranslations } from "next-intl/server";
import Image from "next/image";

const ContactHero: React.FC = async () => {
  const t = await getTranslations();

  return (
    <section className="relative flex min-h-[80vh] items-center justify-center overflow-hidden">
      <div className="absolute inset-0">
        <Image
          src="/img/HoldThePhone.jpg"
          alt="Contact Hero"
          className="object-cover"
          width={1920}
          height={1080}
        />
        <div className="absolute inset-0 bg-black/60" />
      </div>

      <ScrollRevealSection>
        <div className="relative z-10 mx-auto max-w-7xl px-4 text-center text-white sm:px-6 lg:px-8">
          <Badge className="mb-6 rounded-full border-white/30 bg-slate-600/30 px-6 py-2 text-sm font-semibold text-white transition-all duration-300 md:text-base">
            {t("contact.badge")}
          </Badge>

          <h1 className="animate-fade-in mb-6 text-4xl leading-tight font-bold md:text-6xl lg:text-7xl">
            {t("contact.title")}
          </h1>

          <p className="animate-fade-in-delay mx-auto mb-8 max-w-3xl text-base leading-relaxed opacity-90 md:text-2xl">
            {t("contact.description")}
          </p>

          {/* Quick Contact Info */}
          <div className="flex flex-wrap justify-center gap-8 text-sm">
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg">
                <Mail className="text-primary h-4 w-4" />
              </div>
              <span><EMAIL></span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg">
                <Phone className="text-primary h-4 w-4" />
              </div>
              <span>+****************</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 flex h-8 w-8 items-center justify-center rounded-lg">
                <Clock className="text-primary h-4 w-4" />
              </div>
              <span>24/7 Support</span>
            </div>
          </div>
        </div>
      </ScrollRevealSection>
    </section>
  );
};

export default ContactHero;
