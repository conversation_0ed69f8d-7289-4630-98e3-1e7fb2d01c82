import { useQuery } from "@tanstack/react-query";
import { getContactRequests } from "../../services/contactRequests";
import { useSearchParams } from "next/navigation";

export default function useGetContactRequests() {
  const searchParams = useSearchParams();
  const pageNumber = Number(searchParams.get("pageNumber")) || 1;
  const pageSize = Number(searchParams.get("pageSize")) || 10;
  const statusParam = searchParams.get("status");
  const status: DefaultStatus | undefined = (() => {
    if (statusParam === null) return undefined;
    const parsed = Number(statusParam);
    return Number.isNaN(parsed) ? undefined : (parsed as DefaultStatus);
  })();

  return useQuery({
    queryKey: ["contact-requests", pageNumber, pageSize, status],
    queryFn: () =>
      getContactRequests({
        pageNumber,
        pageSize,
        ...(status !== undefined ? { status } : {}),
      }),
    staleTime: 1000 * 60 * 2, // 2 minutes
    retry: 3,
    retryDelay: 1000,
  });
}
