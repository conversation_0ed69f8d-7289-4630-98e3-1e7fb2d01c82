import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { UpdateCorporateRequestStatus } from "../../services/corporateRequests";
import { CorporateRequestStatus } from "../../types/corporateRequests";

export default function useUpdateCorporateRequestStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      status,
    }: {
      id: string;
      status: CorporateRequestStatus;
    }) => UpdateCorporateRequestStatus(id, status),
    onSuccess: () => {
      toast.success("تم تحديث حالة الطلب");
      queryClient.invalidateQueries({
        queryKey: ["corporate-requests"],
      });
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });
}
