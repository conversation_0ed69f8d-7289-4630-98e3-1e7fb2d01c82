"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useLanguage } from "@/providers/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Mail, Send, Gift, Star, Users, Zap } from "lucide-react";

const NewsletterSection = () => {
  const t = useTranslations();
  const { isRTL } = useLanguage();
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      setEmail("");
      // Here you would typically handle the subscription
    }
  };

  const benefits = [
    {
      icon: Gift,
      title: t("newsletter.benefits.exclusiveDeals.title"),
      description: t("newsletter.benefits.exclusiveDeals.description"),
    },
    {
      icon: Star,
      title: t("newsletter.benefits.premiumContent.title"),
      description: t("newsletter.benefits.premiumContent.description"),
    },
    {
      icon: Zap,
      title: t("newsletter.benefits.firstToKnow.title"),
      description: t("newsletter.benefits.firstToKnow.description"),
    },
  ];

  if (isSubscribed) {
    return (
      <section className="from-primary/5 via-background to-brand-warm/20 bg-gradient-to-br py-24">
        <div className="mx-auto max-w-4xl px-4 text-center sm:px-6 lg:px-8">
          <div className="rounded-3xl border border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 p-12">
            <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-emerald-500">
              <Mail className="h-10 w-10 text-white" />
            </div>
            <h2 className="text-foreground mb-4 text-3xl font-bold">
              {t("newsletter.successTitle")}
            </h2>
            <p className="text-muted-foreground mb-6 text-lg">
              {t("newsletter.successDescription")}
            </p>
            <div
              className={`text-muted-foreground flex items-center justify-center text-sm ${
                isRTL ? "space-x-4 space-x-reverse" : "space-x-4"
              }`}
            >
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-1 space-x-reverse" : "space-x-1"
                }`}
              >
                <Users className="h-4 w-4" />
                <span>{t("newsletter.joinSubscribers")}</span>
              </div>
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-1 space-x-reverse" : "space-x-1"
                }`}
              >
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span>{t("newsletter.successRating")}</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="from-primary/5 via-background to-brand-warm/20 relative overflow-hidden bg-gradient-to-br py-24">
      {/* Background decorations */}
      <div className="absolute top-10 left-10 opacity-10">
        <Mail className="text-primary h-24 w-24" />
      </div>
      <div className="absolute right-10 bottom-10 opacity-10">
        <Send className="text-primary h-20 w-20" />
      </div>

      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          {/* Left side - Benefits */}
          <div>
            <div
              className={`from-primary/15 via-primary/10 to-brand-ocean/15 border-primary/20 shadow-soft hover:shadow-elevated group relative mb-6 inline-flex items-center overflow-hidden rounded-full border bg-gradient-to-r px-6 py-3 backdrop-blur-sm transition-all duration-500 hover:scale-105`}
            >
              {/* Animated background glow */}
              <div className="from-primary/5 to-brand-ocean/5 absolute inset-0 rounded-full bg-gradient-to-r opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>

              {/* Shimmer effect */}
              <div className="absolute inset-0 -translate-x-full rounded-full bg-gradient-to-r from-transparent via-white/10 to-transparent transition-transform duration-1000 group-hover:translate-x-full"></div>

              <div
                className={`from-primary to-brand-ocean relative z-10 flex h-5 w-5 items-center justify-center rounded-full bg-gradient-to-r ${
                  isRTL ? "ml-3" : "mr-3"
                } transition-transform duration-300 group-hover:rotate-12`}
              >
                <Mail className="h-3 w-3 text-white" />
              </div>

              <span className="text-primary group-hover:text-foreground relative z-10 text-sm font-bold tracking-wide transition-colors duration-300">
                {t("newsletter.stayConnected")}
              </span>

              {/* Pulse dot indicator */}
              <div
                className={`bg-primary relative z-10 h-2 w-2 rounded-full ${
                  isRTL ? "mr-2" : "ml-2"
                } animate-pulse`}
              >
                <div className="bg-primary absolute inset-0 animate-ping rounded-full opacity-75"></div>
              </div>
            </div>

            <p className="text-muted-foreground mb-8 text-lg leading-relaxed">
              {t("newsletter.description")}
            </p>

            <div className="space-y-4">
              {benefits.map((benefit, index) => (
                <div
                  key={index}
                  className={`bg-background/50 hover:bg-background/80 flex items-start rounded-xl p-4 transition-colors duration-300 ${
                    isRTL ? "space-x-4 space-x-reverse" : "space-x-4"
                  }`}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="from-primary to-brand-ocean flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-lg bg-gradient-to-r">
                    <benefit.icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-foreground mb-1 font-semibold">
                      {benefit.title}
                    </h3>
                    <p className="text-muted-foreground text-sm">
                      {benefit.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Trust indicators */}
            <div
              className={`text-muted-foreground mt-8 flex items-center text-sm ${
                isRTL ? "space-x-6 space-x-reverse" : "space-x-6"
              }`}
            >
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-1 space-x-reverse" : "space-x-1"
                }`}
              >
                <Users className="h-4 w-4" />
                <span>{t("newsletter.subscriberCount")}</span>
              </div>
              <div
                className={`flex items-center ${
                  isRTL ? "space-x-1 space-x-reverse" : "space-x-1"
                }`}
              >
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span>{t("newsletter.rating")}</span>
              </div>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-700"
              >
                {t("newsletter.noSpam")}
              </Badge>
            </div>
          </div>

          {/* Right side - Newsletter form */}
          <div>
            <Card className="shadow-elevated from-background to-brand-warm/30 overflow-hidden border-0 bg-gradient-to-br">
              <CardContent className="p-8">
                <div className="mb-8 text-center">
                  <div className="from-primary to-brand-ocean mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r">
                    <Mail className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-foreground mb-2 text-2xl font-bold">
                    {t("newsletter.formTitle")}
                  </h3>
                  <p className="text-muted-foreground">
                    {t("newsletter.formDescription")}
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="relative">
                    <Input
                      type="email"
                      placeholder={t("newsletter.emailPlaceholder")}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className={`border-border focus:border-primary h-12 transition-colors ${
                        isRTL ? "pl-12" : "pr-12"
                      }`}
                      required
                    />
                    <Mail
                      className={`text-muted-foreground absolute top-1/2 h-5 w-5 -translate-y-1/2 transform ${
                        isRTL ? "left-4" : "right-4"
                      }`}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="bg-gradient-ocean shadow-soft hover:shadow-elevated h-12 w-full font-semibold text-white transition-all duration-300 hover:scale-105 hover:opacity-90"
                  >
                    <Send className={`h-5 w-5 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {t("newsletter.subscribeButton")}
                  </Button>
                </form>

                <div className="mt-6 text-center">
                  <p className="text-muted-foreground text-xs">
                    {t("newsletter.privacyText")}
                    <br />
                    {t("newsletter.unsubscribeText")}
                  </p>
                </div>

                {/* Decorative elements */}
                <div className="from-primary/20 absolute -top-4 -right-4 h-24 w-24 rounded-full bg-gradient-to-br to-transparent"></div>
                <div className="from-brand-ocean/20 absolute -bottom-4 -left-4 h-16 w-16 rounded-full bg-gradient-to-tr to-transparent"></div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSection;
