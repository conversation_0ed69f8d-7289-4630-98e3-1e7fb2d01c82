import ScrollRevealSection from "@/components/ScrollReveal";
import BenefitsSection from "./components/BenefitsSection";
import CTASection from "./components/CTASection";
import HeroSection from "./components/HeroSection";
import PartnerTypesSection from "./components/PartnerTypesSection";
import StepsSection from "./components/StepsSection";

const RealEstatePage: React.FC = () => {
  return (
    <main>
      <HeroSection />

      <ScrollRevealSection>
        <BenefitsSection />
      </ScrollRevealSection>
      <ScrollRevealSection>
        <PartnerTypesSection />
      </ScrollRevealSection>
      <ScrollRevealSection>
        <StepsSection />
      </ScrollRevealSection>
      <ScrollRevealSection>
        <CTASection />
      </ScrollRevealSection>
    </main>
  );
};

export default RealEstatePage;
