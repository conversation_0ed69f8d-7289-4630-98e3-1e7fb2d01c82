"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Link, usePathname } from "@/i18n/navigation";
import { Globe } from "lucide-react";
import { useLocale } from "next-intl";
import { useSearchParams } from "next/navigation";

export default function LangSwitcher() {
  const locale = useLocale();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const nextLocale = locale === "ar" ? "en" : "ar";
  const href =
    pathname + (searchParams.toString() ? `?${searchParams.toString()}` : "");

  return (
    <Button
      variant="ghost"
      size="sm"
      className="rounded-lg px-3 py-2 transition-colors hover:bg-[#279fc7]/10 hover:text-[#279fc7]"
      asChild
    >
      <Link href={href} locale={nextLocale} hrefLang={nextLocale}>
        <Globe className="h-[1.2rem] w-[1.2rem]" />
        <span className="lg:hidden xl:block">
          {locale === "ar" ? "En" : "AR"}
        </span>
      </Link>
    </Button>
  );
}
