import api from "@/lib/axios";
import { isAxiosError } from "axios";
import { DefaultStatus, PaginationData } from "../types/global";
import { InvestorRequest } from "../types/investorRequests";

export type GetInvestorRequestsResponse = {
  items: InvestorRequest[];
} & PaginationData;

export const getInvestorRequests = async ({
  pageNumber = 1,
  pageSize = 10,
  status,
}: {
  pageNumber?: number;
  pageSize?: number;
  status?: DefaultStatus;
}) => {
  try {
    const res = await api.get<GetInvestorRequestsResponse>(
      "/investor-requests",
      {
        params: {
          pageNumber,
          pageSize,
          ...(status !== undefined ? { status } : {}),
        },
      },
    );

    return res.data;
  } catch (err) {
    console.error(err);

    if (isAxiosError(err)) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};

export const deleteInvestorRequest = async (id: string) => {
  try {
    const res = await api.delete(`/investor-requests/${id}`);

    return res.data;
  } catch (err) {
    console.error(err);

    if (isAxiosError(err)) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
      }

      if (err.response?.status === 404) {
        throw new Error("غير موجود");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};

export const updateInvestorRequestStatus = async (
  id: string,
  status: DefaultStatus,
) => {
  try {
    const res = await api.patch(`/investor-requests/${id}`, {
      status: typeof status === "string" ? parseInt(status) : status,
    });

    return res.data;
  } catch (err) {
    console.error(err);

    if (isAxiosError(err)) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
      }

      if (err.response?.status === 404) {
        throw new Error("غير موجود");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};
