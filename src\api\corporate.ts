import api from "@/lib/axios";

const BASE_ROUTE = "/corporate-pricing-requests";

interface CorporatePricingRequest {
  fullName: string;
  email: string;
  phone: string;
  company: string;
  websiteOrLinkedIn?: string;
  message: string;
}

export const createCorporatePricingRequestApi = async (
  request: CorporatePricingRequest,
) => {
  const { data } = await api.post(BASE_ROUTE, request);
  return data;
};

export const getCorporatePricingRequestsApi = async () => {
  const { data } = await api.get(BASE_ROUTE);
  return data;
};

export const getCorporatePricingRequestByIdApi = async (id: string) => {
  const { data } = await api.get(`${BASE_ROUTE}/${id}`);
  return data;
};

export const deleteCorporatePricingRequestApi = async (id: string) => {
  const { data } = await api.delete(`${BASE_ROUTE}/${id}`);
  return data;
};

export const updateCorporatePricingRequestApi = async (
  id: string,
  request: CorporatePricingRequest,
) => {
  const { data } = await api.put(`${BASE_ROUTE}/${id}`, request);
  return data;
};
