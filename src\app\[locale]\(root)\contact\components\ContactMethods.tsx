"use client";
import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { Mail, Phone, MessageCircle, LucideIcon } from "lucide-react";

interface ContactMethod {
  icon: LucideIcon;
  title: string;
  description: string;
  value: string;
  color: string;
}

const ContactMethods: React.FC = () => {
  const t = useTranslations();

  const contactMethods: ContactMethod[] = [
    {
      icon: Mail,
      title: t("contact.methods.email.title"),
      description: t("contact.methods.email.description"),
      value: "<EMAIL>",
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: Phone,
      title: t("contact.methods.phone.title"),
      description: t("contact.methods.phone.description"),
      value: "+****************",
      color: "from-green-500 to-green-600",
    },
    {
      icon: MessageCircle,
      title: t("contact.methods.whatsapp.title"),
      description: t("contact.methods.whatsapp.description"),
      value: "+****************",
      color: "from-green-600 to-green-500",
    },
  ];

  return (
    <section className="bg-muted/30 py-24">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("contact.methods.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl leading-relaxed">
            {t("contact.methods.description")}
          </p>
        </div>

        <div className="mb-16 grid gap-8 md:grid-cols-3">
          {contactMethods.map((method, index) => (
            <Card
              key={index}
              className="shadow-soft bg-muted-foreground/5 hover:shadow-elevated group border-0 transition-all duration-300 hover:scale-105"
            >
              <CardContent className="p-8 text-center">
                <div
                  className={`h-16 w-16 bg-gradient-to-br ${method.color} shadow-soft mx-auto mb-6 flex items-center justify-center rounded-2xl transition-transform duration-300 group-hover:scale-110`}
                >
                  <method.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-foreground mb-4 text-xl font-bold">
                  {method.title}
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {method.description}
                </p>
                <p className="text-primary text-lg font-semibold">
                  {method.value}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ContactMethods;
