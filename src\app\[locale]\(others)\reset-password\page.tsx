"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { FloatingPasswordInput } from "@/components/ui/floating-password-input";
import { useResetPassword } from "@/hooks/auth/use-reset-password";
import { createAuthValidations } from "@/lib/validations/auth";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Loader2, Lock, Shield } from "lucide-react";
import FormErrors from "@/components/FormErrors";
import { useTranslations } from "next-intl";
import { useMemo } from "react";

export default function ResetPasswordPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const email = searchParams.get("email");
  const t = useTranslations();

  const { resetPasswordSchema } = useMemo(() => createAuthValidations(t), [t]);
  type FormValues = z.infer<typeof resetPasswordSchema>;

  const form = useForm<FormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const resetPasswordMutation = useResetPassword();

  const onSubmit = (values: FormValues) => {
    if (!token || !email) {
      form.setError("root", {
        message: "Missing token or email.",
      });
      return;
    }
    resetPasswordMutation.mutate({
      email,
      token,
      newPassword: values.password,
    });
  };

  return (
    <div className="relative flex min-h-screen items-center justify-center overflow-hidden">
      {/* Enhanced Background */}
      <div className="from-primary/5 via-background to-primary/10 absolute inset-0 bg-gradient-to-br" />
      <div className="from-primary/10 absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] via-transparent to-transparent" />

      {/* Floating Elements */}
      <div className="bg-primary/5 absolute top-20 left-10 h-32 w-32 rounded-full blur-3xl" />
      <div className="bg-primary/5 absolute right-10 bottom-20 h-40 w-40 rounded-full blur-3xl" />

      <Card className="shadow-elevated bg-background/95 relative z-10 w-full max-w-md border-0 backdrop-blur-sm">
        <CardHeader className="text-center">
          <div className="bg-primary/10 mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">
            <Shield className="text-primary h-6 w-6" />
          </div>
          <CardTitle className="text-2xl font-bold">
            {t("auth.resetPassword") || "Reset Your Password"}
          </CardTitle>
          <CardDescription>
            {t("auth.resetPasswordDescription") ||
              "Enter a new password for your account."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {resetPasswordMutation.isSuccess ? (
            <div className="space-y-4 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-green-600">
                  {t("auth.passwordResetSuccess") ||
                    "Password Reset Successful"}
                </h3>
                <p className="text-muted-foreground mt-2 text-sm">
                  {t("auth.passwordResetSuccessMessage") ||
                    "Your password has been reset successfully. You can now log in with your new password."}
                </p>
              </div>
            </div>
          ) : (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                {/* Error Handling */}
                {resetPasswordMutation.isError && (
                  <FormErrors error={resetPasswordMutation.error} />
                )}

                {/* Root form errors */}
                {form.formState.errors.root?.message && (
                  <div className="bg-destructive/10 text-destructive rounded-md p-3 text-sm">
                    {form.formState.errors.root.message}
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingPasswordInput
                          label={t("auth.password") || "New Password"}
                          icon={Lock}
                          error={fieldState.error?.message}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingPasswordInput
                          label={
                            t("auth.confirmPassword") || "Confirm New Password"
                          }
                          icon={Lock}
                          error={fieldState.error?.message}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="bg-primary text-primary-foreground hover:bg-primary/90 h-12 w-full"
                  disabled={resetPasswordMutation.isPending}
                >
                  {resetPasswordMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t("auth.resettingPassword") || "Resetting Password..."}
                    </>
                  ) : (
                    <>
                      <Shield className="mr-2 h-4 w-4" />
                      {t("auth.resetPassword") || "Reset Password"}
                    </>
                  )}
                </Button>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
