import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createInvestorRequestApi } from "@/api/investor";
import { toast } from "sonner";
import { isAxiosError } from "axios";
import { useTranslations } from "next-intl";

export const useCreateInvestor = () => {
  const t = useTranslations("errors");
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createInvestorRequestApi,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["investorPricingRequests"] });
    },
    onError: (error) => {
      console.error(error);
      if (!isAxiosError(error)) toast.error(t("generalError"));
    },
  });
};
