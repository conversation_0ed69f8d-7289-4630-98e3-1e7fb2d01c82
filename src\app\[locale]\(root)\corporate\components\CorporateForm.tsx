"use client";

import FormErrors from "@/components/FormErrors";
import FormFeedback from "@/components/FormFeedback";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FloatingInput } from "@/components/ui/floating-input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { useCreateCorporate } from "@/hooks/corporates";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ArrowRight,
  Building2,
  Link,
  Mail,
  MessageSquare,
  Phone,
  Send,
  User,
} from "lucide-react";
import type { createTranslator } from "next-intl";
import { useTranslations } from "next-intl";
import { useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

function createCorporateFormSchema(t: ReturnType<typeof createTranslator>) {
  return z.object({
    fullName: z
      .string()
      .min(1, t("validations.required"))
      .max(100, t("validations.maxLength", { max: 100 })),
    email: z
      .string()
      .min(1, t("validations.required"))
      .email(t("validations.email")),
    phone: z
      .string()
      .min(1, t("validations.required"))
      .regex(
        /^[0-9]{10,15}$/,
        t("validations.phoneDigitsRange", { min: 10, max: 15 }),
      ),
    company: z
      .string()
      .min(2, t("validations.minLength", { min: 2 }))
      .max(100, t("validations.maxLength", { max: 100 })),
    websiteOrLinkedIn: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val) return true; // optional field
          try {
            const u = new URL(val);
            const protocol = u.protocol.replace(":", "");
            return (
              (protocol === "http" || protocol === "https") &&
              Boolean(u.hostname)
            );
          } catch {
            return false;
          }
        },
        { message: t("validations.url") },
      ),
    message: z
      .string()
      .min(5, t("validations.minLength", { min: 5 }))
      .max(1000, t("validations.maxLength", { max: 1000 })),
  });
}

type CorporateFormSchema = ReturnType<typeof createCorporateFormSchema>;
type CorporateFormValues = z.infer<CorporateFormSchema>;

export function CorporateForm({
  buttonClassName,
}: {
  buttonClassName?: string;
}) {
  const t = useTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const {
    mutate: createCorporate,
    isPending,
    error,
    isError,
    reset,
  } = useCreateCorporate();
  const [isSuccess, setIsSuccess] = useState(false);

  const formSchema = useMemo(() => createCorporateFormSchema(t), [t]);

  const form = useForm<CorporateFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      company: "",
      websiteOrLinkedIn: "",
      message: "",
    },
  });

  function onSubmit(values: CorporateFormValues) {
    createCorporate(values, {
      onSuccess: () => {
        form.reset();
        reset();
        setIsSuccess(true);
        setIsOpen(false);
      },
    });
  }

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) {
            form.reset();
            reset();
            setIsOpen(false);
            setIsSuccess(false);
          }
          setIsOpen(open);
        }}
      >
        <DialogTrigger asChild>
          <Button
            className={cn(
              "group rounded-full bg-white !px-6 py-7 text-lg font-semibold text-[#279fc7] shadow-lg transition-all duration-300 hover:scale-105 hover:bg-gray-100 hover:shadow-xl",
              buttonClassName,
            )}
          >
            {t("corporate.cta.button")}
            <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1 rtl:rotate-180 rtl:group-hover:-translate-x-1" />
          </Button>
        </DialogTrigger>
        <DialogContent className="p-6 sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-bold">
              {t("contact.form.title")}
            </DialogTitle>
            <DialogDescription className="mb-4 text-center">
              {t("contact.form.description")}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingInput
                          label={t("contact.form.name")}
                          icon={User}
                          error={fieldState.error?.message}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingInput
                          label={t("contact.form.companyName")}
                          icon={Building2}
                          error={fieldState.error?.message}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingInput
                          type="tel"
                          label={t("contact.form.email")}
                          icon={Mail}
                          error={fieldState.error?.message}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <FloatingInput
                          type="tel"
                          label={t("contact.form.phone")}
                          icon={Phone}
                          error={fieldState.error?.message}
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="websiteOrLinkedIn"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        type="tel"
                        label={t("contact.form.companyWebsite")}
                        icon={Link}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="message"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        className="h-18"
                        type="textarea"
                        label={t("contact.form.message")}
                        icon={MessageSquare}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {isError && <FormErrors error={error} />}

              <Button className="w-full p-6" type="submit" disabled={isPending}>
                {isPending ? (
                  <>
                    <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                    {t("contact.form.sending")}
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-5 w-5" />
                    {t("contact.form.submit")}
                  </>
                )}
              </Button>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <FormFeedback open={isSuccess} onOpenChange={setIsSuccess} />
    </>
  );
}
