import { But<PERSON> } from "@/components/ui/button";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Trash2 } from "lucide-react";
import useDeleteCorporateRequest from "../../hooks/corporateRequests/useDeleteCorporateRequest";
import { useState } from "react";
import { ConfirmDialog } from "../../components/ConfirmDialog";

export default function DeleteCorporateRequestButton({ id }: { id: string }) {
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const { mutate, isPending } = useDeleteCorporateRequest();

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label="حذف"
            variant="destructive"
            size="icon"
            className="rounded-full"
            onClick={() => setIsConfirmDialogOpen(true)}
          >
            <Trash2 className="size-4" />
            <span className="sr-only">حذف</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent variant="destructive">حذف</TooltipContent>
      </Tooltip>
      {isConfirmDialogOpen && (
        <ConfirmDialog
          destructive
          isLoading={isPending}
          title="حذف طلب"
          description="هل أنت متأكد من حذف هذا الطلب؟"
          confirmText="حذف"
          cancelText="إلغاء"
          onCancelAction={() => setIsConfirmDialogOpen(false)}
          onConfirmAction={() => {
            mutate(id, {
              onSuccess: () => setIsConfirmDialogOpen(false),
            });
          }}
        />
      )}
    </>
  );
}
