"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Trash2 } from "lucide-react";
import { useState } from "react";
import { ConfirmDialog } from "../../components/ConfirmDialog";
import useDeleteInvestorRequest from "../../hooks/investorRequests/useDeleteInvestorRequest";

interface DeleteInvestorRequestButtonProps {
  id: string;
}

export default function DeleteInvestorRequestButton({
  id,
}: DeleteInvestorRequestButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { mutate: deleteRequest, isPending } = useDeleteInvestorRequest();

  const handleDelete = () => {
    deleteRequest(id, {
      onSuccess: () => setIsOpen(false),
    });
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="destructive"
            size="icon"
            className="rounded-full"
            onClick={() => setIsOpen(true)}
            disabled={isPending}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">حذف</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent variant="destructive">حذف الطلب</TooltipContent>
      </Tooltip>

      {isOpen && (
        <ConfirmDialog
          destructive
          title="هل أنت متأكد من حذف هذا الطلب؟"
          description="لا يمكن التراجع عن هذا الإجراء"
          onConfirmAction={handleDelete}
          onCancelAction={() => setIsOpen(false)}
          isLoading={isPending}
        />
      )}
    </>
  );
}
