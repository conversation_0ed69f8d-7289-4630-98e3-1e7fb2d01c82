import { Button } from "@/components/ui/button";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ColumnDef } from "@tanstack/react-table";
import { Pencil } from "lucide-react";
import {
  createSelectColumn,
  createTextColumn,
} from "../../components/data-table";
import { User, UserStatus } from "../../types/users";
import DeleteUserButton from "./DeleteUserButton";

export const usersColumns: ColumnDef<User>[] = [
  // createSelectColumn<User>(),
  createTextColumn<User>("firstName", "الاسم الأول"),
  createTextColumn<User>("lastName", "الاسم الأخير"),
  createTextColumn<User>("email", "البريد الإلكتروني"),
  createTextColumn<User>("phoneNumber", "رقم الهاتف"),
  {
    accessorKey: "status",
    header: "حالة الحساب",
    cell: ({ row }) => {
      const status = row.getValue("status") as UserStatus;

      const statusLabels: Record<UserStatus, string> = {
        [UserStatus.NotApproved]: "غير معتمد",
        [UserStatus.Pending]: "معلق",
        [UserStatus.Approved]: "معتمد",
        [UserStatus.Rejected]: "مرفوض",
      };

      const statusClasses: Record<UserStatus, string> = {
        [UserStatus.NotApproved]: "bg-gray-100 text-gray-800",
        [UserStatus.Pending]: "bg-yellow-100 text-yellow-800",
        [UserStatus.Approved]: "bg-green-100 text-green-800",
        [UserStatus.Rejected]: "bg-red-100 text-red-800",
      };
      return (
        <span
          className={`rounded-full px-2 py-1 text-xs font-medium ${statusClasses[status]}`}
        >
          {statusLabels[status]}
        </span>
      );
    },
  },
  {
    accessorKey: "roles",
    header: "الصلاحيات",
    cell: ({ row }) => {
      const roles = row.getValue("roles") as string[];
      return (
        <>
          {roles.map((role) => (
            <span
              className={`rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800`}
              key={role}
            >
              {role}
            </span>
          ))}
        </>
      );
    },
  },
  {
    header: "اجراءات",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-1.5">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                aria-label="تعديل"
                variant="default"
                size="icon"
                className="rounded-full"
                onClick={() => console.log("edit", row.original)}
              >
                <Pencil className="size-4" />
                <span className="sr-only">تعديل</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent variant="primary">تعديل</TooltipContent>
          </Tooltip>

          <DeleteUserButton id={row.original.id} />
        </div>
      );
    },
  },
];
