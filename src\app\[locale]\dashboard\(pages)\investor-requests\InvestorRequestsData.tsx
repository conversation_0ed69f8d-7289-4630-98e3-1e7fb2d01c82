"use client";

import GenericPageLoading from "@/components/GenericPageLoading";
import ApiError from "../../components/ApiError";
import { DataTable } from "../../components/data-table";
import GenericStatusFilter from "../../components/GenericStatusFilter";
import useGetInvestorRequests from "../../hooks/investorRequests/useGetInvestorRequests";
import { investorRequestsColumns } from "./InvestorRequestsColumns";

export default function InvestorRequestsData() {
  const { data, isLoading, error } = useGetInvestorRequests();

  if (isLoading) {
    return <GenericPageLoading />;
  }

  if (error || !data) {
    return <ApiError error={error as Error} />;
  }

  return (
    <div>
      <DataTable
        columns={investorRequestsColumns}
        data={data.items}
        pagination={{
          pageNumber: data.pageNumber,
          totalPages: data.totalPages,
          hasPreviousPage: data.hasPreviousPage,
          hasNextPage: data.hasNextPage,
        }}
        beforeTable={<GenericStatusFilter />}
      />
    </div>
  );
}
