import whatsAppGif from "@/../public/whatsapp.gif";
import { Card, CardContent } from "@/components/ui/card";
import {
  CalendarCheck,
  CalendarDays,
  CheckCircle,
  Headphones,
  Key,
  Smartphone,
  Star,
} from "lucide-react";
import { getMessages } from "next-intl/server";
import Image from "next/image";

const AppSection: React.FC = async () => {
  const messages = await getMessages();
  const appSectionMessages = messages.howItWorks.app;

  const appFeatures = [
    {
      icon: CalendarCheck,
      title: appSectionMessages.features.booking.title,
      description: appSectionMessages.features.booking.description,
    },
    {
      icon: Key,
      title: appSectionMessages.features.checkIn.title,
      description: appSectionMessages.features.checkIn.description,
    },
    {
      icon: CalendarDays,
      title: appSectionMessages.features.trips.title,
      description: appSectionMessages.features.trips.description,
    },
    {
      icon: Headphones,
      title: appSectionMessages.features.support.title,
      description: appSectionMessages.features.support.description,
    },
  ];

  return (
    <section className="bg-background py-20 lg:py-32">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
            {appSectionMessages.title}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
            {appSectionMessages.subtitle}
          </p>
        </div>

        <div className="grid items-center gap-16 md:grid-cols-2">
          <div className="space-y-8">
            {appFeatures.map((feature, index) => (
              <Card
                key={feature.title}
                className="group shadow-soft hover:shadow-elevated bg-secondary/70 overflow-hidden border-2 transition-all duration-500 hover:scale-105"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <CardContent className="p-6">
                  <div className={`flex items-center space-x-5`}>
                    <div className="relative">
                      <div className="from-primary/10 to-brand-ocean/10 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br transition-transform duration-300 group-hover:scale-110">
                        <feature.icon className="text-primary group-hover:text-brand-ocean h-8 w-8 transition-colors duration-300" />
                      </div>
                      <div className="from-primary to-brand-ocean absolute -top-1 -right-1 flex h-6 w-6 items-center justify-center rounded-full bg-gradient-to-br opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-foreground group-hover:text-primary mb-2 text-lg font-semibold transition-colors duration-300">
                        {feature.title}
                      </h4>
                      <p className="text-muted-foreground text-sm leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="relative flex justify-center">
            <div className="relative">
              {/* Floating elements for visual appeal */}
              <div className="from-primary/20 to-brand-ocean/20 absolute -top-6 -left-6 h-12 w-12 animate-pulse rounded-full bg-gradient-to-br blur-lg"></div>
              <div
                className="from-brand-teal-dark/20 to-primary/20 absolute -right-6 -bottom-6 h-16 w-16 animate-pulse rounded-full bg-gradient-to-br blur-lg"
                style={{ animationDelay: "1s" }}
              ></div>

              {/* Phone container with mobile proportions */}
              <div className="group relative h-[520px] w-64">
                {/* Phone shadow */}
                <div className="from-primary/15 to-brand-ocean/15 absolute inset-0 translate-y-4 scale-95 transform rounded-[36px] bg-gradient-to-br blur-xl transition-transform duration-500 group-hover:scale-100"></div>

                {/* Phone frame - realistic mobile dimensions */}
                <div className="group-hover:shadow-3xl relative h-full w-full rounded-[36px] bg-gradient-to-br from-slate-900 to-black p-2 shadow-2xl transition-shadow duration-500">
                  {/* Screen bezel */}
                  <div className="relative h-full w-full overflow-hidden rounded-[32px] bg-black">
                    {/* Dynamic island */}
                    <div className="absolute top-3 left-1/2 z-20 h-5 w-20 -translate-x-1/2 transform rounded-full bg-black shadow-inner"></div>

                    {/* Screen content */}
                    <div className="relative h-full w-full overflow-hidden rounded-[30px] bg-white">
                      <Image
                        src={whatsAppGif}
                        alt="App demonstration"
                        className="absolute inset-0 z-10 h-full w-full rounded-[30px] object-cover"
                      />

                      {/* Screen reflection effect */}
                      <div className="pointer-events-none absolute inset-0 z-15 rounded-[30px] bg-gradient-to-br from-white/10 via-transparent to-transparent"></div>
                    </div>
                  </div>

                  {/* Phone physical buttons */}
                  <div className="absolute top-16 -left-0.5 h-6 w-0.5 rounded-full bg-slate-700"></div>
                  <div className="absolute top-28 -left-0.5 h-8 w-0.5 rounded-full bg-slate-700"></div>
                  <div className="absolute top-40 -left-0.5 h-8 w-0.5 rounded-full bg-slate-700"></div>
                  <div className="absolute top-32 -right-0.5 h-12 w-0.5 rounded-full bg-slate-700"></div>
                </div>

                {/* Floating UI elements - smaller and more subtle */}
                <div className="from-primary to-brand-ocean absolute -top-3 -right-3 flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br opacity-0 shadow-lg transition-opacity duration-500 group-hover:opacity-100">
                  <Smartphone className="h-4 w-4 text-white" />
                </div>

                <div
                  className="from-brand-teal-dark to-primary absolute -bottom-3 -left-3 flex h-7 w-7 items-center justify-center rounded-md bg-gradient-to-br opacity-0 shadow-lg transition-opacity duration-700 group-hover:opacity-100"
                  style={{ animationDelay: "200ms" }}
                >
                  <Star className="h-3 w-3 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppSection;
