"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { Check, Pencil } from "lucide-react";
import { useState } from "react";
import useUpdateInvestorRequestStatus from "../../hooks/investorRequests/useUpdateInvestorRequestStatus";
import {
  DefaultStatus,
  defaultStatusClasses,
  defaultStatusLabels,
} from "../../types/global";

type UpdateInvestorRequestStatusButtonProps = {
  id: string;
  currentStatus: DefaultStatus;
};

export default function UpdateInvestorRequestStatusButton({
  id,
  currentStatus,
}: UpdateInvestorRequestStatusButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [status, setStatus] = useState<DefaultStatus>(currentStatus);
  const { mutate: updateStatus, isPending } = useUpdateInvestorRequestStatus();

  const handleSave = () => {
    if (status === currentStatus) return;

    updateStatus({ id, status }, { onSuccess: () => setIsDialogOpen(false) });
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label="تغيير الحالة"
            variant="default"
            size="icon"
            className="rounded-full"
            onClick={() => setIsDialogOpen(true)}
          >
            <Pencil className="size-4" />
            <span className="sr-only">تغيير الحالة</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent variant="primary">تغيير الحالة</TooltipContent>
      </Tooltip>

      {isDialogOpen && (
        <Dialog
          open
          onOpenChange={(open) => {
            setStatus(currentStatus);
            setIsDialogOpen(open);
          }}
        >
          <DialogContent>
            <DialogHeader className="items-start">
              <DialogTitle>تغيير الحالة</DialogTitle>
              <DialogDescription>قم بتعديل حالة الطلب</DialogDescription>
            </DialogHeader>

            <div className="flex flex-wrap gap-2">
              {Object.entries(defaultStatusLabels).map(([key, value]) => {
                const statusValue = Number(key) as DefaultStatus;
                return (
                  <button
                    type="button"
                    key={key}
                    onClick={() => setStatus(statusValue)}
                    className={cn(
                      "flex items-center gap-2 rounded-full px-4.5 py-1.5 text-sm font-medium sm:text-base",
                      defaultStatusClasses[statusValue],
                    )}
                  >
                    {value}
                    {status === statusValue && <Check className="size-4" />}
                  </button>
                );
              })}
            </div>

            <DialogFooter className="justify-start">
              <Button
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                disabled={isPending}
              >
                إلغاء
              </Button>
              <Button
                disabled={isPending || status === currentStatus}
                onClick={handleSave}
              >
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
