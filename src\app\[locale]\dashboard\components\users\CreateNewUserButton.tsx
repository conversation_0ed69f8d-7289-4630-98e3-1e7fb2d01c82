import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { PlusSquareIcon } from "lucide-react";

export default function CreateNewUserButton({
  className,
}: {
  className?: string;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className={className}>
          إضافة مستخدم
          <PlusSquareIcon />
        </Button>
      </DialogTrigger>
      <DialogContent lang="ar">
        <DialogHeader>
          <DialogTitle className="text-center">إضافة مستخدم جديد</DialogTitle>
          <DialogDescription className="sr-only text-center">
            قم بلإضافة مستخدم جديد
          </DialogDescription>
        </DialogHeader>

        <div>sf</div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="secondary">إلغاء</Button>
          </DialogClose>
          <DialogClose asChild>
            <Button>تأكيد</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
