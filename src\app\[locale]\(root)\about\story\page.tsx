import ScrollRevealSection from "@/components/ScrollReveal";
import CTASection from "./components/CTASection";
import HeroSection from "./components/HeroSection";
import MilestonesSection from "./components/MilestonesSection";
import MissionSection from "./components/MissionSection";
import ValuesSection from "./components/ValuesSection";

const StoryPage: React.FC = () => {
  return (
    <main>
      <HeroSection />

      <ScrollRevealSection>
        <MissionSection />
      </ScrollRevealSection>

      <ScrollRevealSection>
        <ValuesSection />
      </ScrollRevealSection>

      <ScrollRevealSection>
        <MilestonesSection />
      </ScrollRevealSection>

      <ScrollRevealSection>
        <CTASection />
      </ScrollRevealSection>
    </main>
  );
};

export default StoryPage;
