import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import React from "react";

export default function ApiError({ error }: { error: Error }) {
  return (
    <Alert
      className="bg-destructive/20 border-destructive mx-auto my-4 w-full max-w-lg"
      variant="destructive"
    >
      <AlertTitle>خطأ</AlertTitle>
      <AlertDescription>
        {error?.message || "حدث خطأ غير متوقع"}
      </AlertDescription>
    </Alert>
  );
}
