import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Award, Rocket } from "lucide-react";
import { getTranslations } from "next-intl/server";

interface Step {
  icon: React.ElementType;
  title: string;
  description: string;
}

const StepsSection: React.FC = async () => {
  const t = await getTranslations("realEstate.steps");

  const steps: Step[] = [
    {
      icon: FileText,
      title: t("submit.title"),
      description: t("submit.description"),
    },
    {
      icon: Clipboard<PERSON>heck,
      title: t("review.title"),
      description: t("review.description"),
    },
    {
      icon: Award,
      title: t("onboarding.title"),
      description: t("onboarding.description"),
    },
    {
      icon: Rocket,
      title: t("launch.title"),
      description: t("launch.description"),
    },
  ];

  return (
    <section className="bg-muted/30 py-20">
      <div className="container mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold md:text-4xl">{t("title")}</h2>
          <p className="text-muted-foreground mt-2 text-lg">
            {t("description")}
          </p>
        </div>
        <div className="relative">
          <div className="absolute top-1/2 left-0 hidden h-0.5 w-full md:block"></div>
          <div className="relative grid grid-cols-1 gap-8 md:grid-cols-4">
            {steps.map((step, index) => (
              <Card
                key={index}
                className="bg-card text-card-foreground transform border p-8 text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl"
              >
                <div className="relative inline-block">
                  <div className="bg-secondary border-muted-foreground/20 mx-auto flex h-16 w-16 items-center justify-center rounded-full border-2">
                    <step.icon className="text-primary h-8 w-8" />
                  </div>
                </div>
                <h3 className="mt-4 mb-2 text-xl font-semibold">
                  {step.title}
                </h3>
                <p className="text-secondary-foreground/80">
                  {step.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default StepsSection;
