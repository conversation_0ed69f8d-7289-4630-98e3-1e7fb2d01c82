import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ResponseErrors } from "@/types/global";
import { isAxiosError } from "axios";
import { useLocale, useTranslations } from "next-intl";

export default function FormErrors({ error }: { error: unknown }) {
  const t = useTranslations("errors");
  const locale = useLocale();
  const resErrors = isAxiosError(error)
    ? (error.response?.data.errors as ResponseErrors)
    : [{ description: t("generalError") }];

  return (
    <Alert variant="destructive" className="bg-red-500/40 text-red-600">
      <AlertTitle className="text-base">
        {locale === "ar" ? "خطأ" : "Error"}
      </AlertTitle>
      <AlertDescription>
        <ul className="list-inside list-disc space-y-2 text-sm">
          {resErrors.map((error, idx) => (
            <li key={idx}>{error.description}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}
