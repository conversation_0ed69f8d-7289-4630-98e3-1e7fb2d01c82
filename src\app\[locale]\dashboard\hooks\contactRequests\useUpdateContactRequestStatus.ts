import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { updateContactRequestStatus } from "../../services/contactRequests";
import { DefaultStatus } from "../../types/global";

export default function useUpdateContactRequestStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      status,
    }: {
      id: string;
      status: DefaultStatus;
    }) => updateContactRequestStatus(id, status),
    onSuccess: () => {
      toast.success("تم تحديث حالة الطلب بنجاح");
      // Invalidate both the contact requests list and any individual queries
      queryClient.invalidateQueries({
        queryKey: ["contact-requests"],
      });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
