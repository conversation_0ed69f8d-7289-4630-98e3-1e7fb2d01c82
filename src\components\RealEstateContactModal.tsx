import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Send, Mail, Phone, User, MessageSquare, MapPin, X } from "lucide-react";

interface RealEstateContactModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const RealEstateContactModal = ({ isOpen, onClose }: RealEstateContactModalProps) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    mobile: "",
    propertyLocation: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000));
      setIsSubmitted(true);
      
      // Reset form after 3 seconds and close modal
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({
          name: "",
          email: "",
          mobile: "",
          propertyLocation: "",
          message: "",
        });
        onClose();
      }, 3000);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setIsSubmitted(false);
      setFormData({
        name: "",
        email: "",
        mobile: "",
        propertyLocation: "",
        message: "",
      });
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-2xl shadow-2xl border-0">
        <DialogHeader className="relative pb-6">
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 top-4 h-8 w-8 rounded-full hover:bg-gray-100"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
          <DialogTitle className="text-3xl font-bold text-center text-gray-900 mb-2">
            {t("realEstate.contactModal.title")}
          </DialogTitle>
          <DialogDescription className="text-center text-gray-600 text-lg">
            {t("realEstate.contactModal.description")}
          </DialogDescription>
        </DialogHeader>

        {isSubmitted ? (
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Send className="w-10 h-10 text-green-600" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">
              {t("realEstate.contactModal.successTitle")}
            </h3>
            <p className="text-gray-600 text-lg">
              {t("realEstate.contactModal.successMessage")}
            </p>
          </div>
        ) : (
          <Card className="border-0 shadow-none">
            <CardContent className="p-0">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name" className="flex items-center text-base font-medium text-gray-700">
                    <User className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'} text-primary`} />
                    {t("realEstate.contactModal.name")} {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.namePlaceholder")}
                    className="h-12 text-base border-2 border-gray-200 focus:border-primary rounded-xl transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center text-base font-medium text-gray-700">
                    <Mail className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'} text-primary`} />
                    {t("realEstate.contactModal.email")} {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.emailPlaceholder")}
                    className="h-12 text-base border-2 border-gray-200 focus:border-primary rounded-xl transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mobile" className="flex items-center text-base font-medium text-gray-700">
                    <Phone className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'} text-primary`} />
                    {t("realEstate.contactModal.mobile")} {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="mobile"
                    type="tel"
                    name="mobile"
                    value={formData.mobile}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.mobilePlaceholder")}
                    className="h-12 text-base border-2 border-gray-200 focus:border-primary rounded-xl transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="propertyLocation" className="flex items-center text-base font-medium text-gray-700">
                    <MapPin className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'} text-primary`} />
                    {t("realEstate.contactModal.propertyLocation")} {t("realEstate.contactModal.required")}
                  </Label>
                  <Input
                    id="propertyLocation"
                    name="propertyLocation"
                    value={formData.propertyLocation}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.propertyLocationPlaceholder")}
                    className="h-12 text-base border-2 border-gray-200 focus:border-primary rounded-xl transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message" className="flex items-center text-base font-medium text-gray-700">
                    <MessageSquare className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'} text-primary`} />
                    {t("realEstate.contactModal.message")} {t("realEstate.contactModal.required")}
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder={t("realEstate.contactModal.messagePlaceholder")}
                    className="min-h-[120px] text-base border-2 border-gray-200 focus:border-primary rounded-xl transition-colors resize-none"
                    required
                  />
                </div>

                <div className="pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full h-14 text-lg font-semibold bg-primary hover:bg-primary/90 text-white rounded-xl transition-all duration-300 hover:scale-[1.02] shadow-lg hover:shadow-xl"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center">
                        <div className={`w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {t("realEstate.contactModal.sending")}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        <Send className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {t("realEstate.contactModal.sendMessage")}
                      </div>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default RealEstateContactModal;