import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { getInvestorRequests } from "../../services/investorRequests";
import { DefaultStatus } from "../../types/global";

export default function useGetInvestorRequests() {
  const searchParams = useSearchParams();
  const pageNumber = Number(searchParams.get("pageNumber")) || 1;
  const pageSize = Number(searchParams.get("pageSize")) || 10;
  const statusParam = searchParams.get("status");
  const status: DefaultStatus | undefined = (() => {
    if (statusParam === null) return undefined;
    const parsed = Number(statusParam);
    return Number.isNaN(parsed) ? undefined : (parsed as DefaultStatus);
  })();

  return useQuery({
    queryKey: ["investor-requests", pageNumber, pageSize, status],
    queryFn: () =>
      getInvestorRequests({
        pageNumber,
        pageSize,
        ...(status !== undefined ? { status } : {}),
      }),
    staleTime: 1000 * 60 * 2, // 2 minutes
    retry: 3,
    retryDelay: 1000,
  });
}
