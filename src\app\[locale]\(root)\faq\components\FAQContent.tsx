"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import { useFaq } from "@/hooks/faq";
import { useParams } from "next/navigation";

interface FAQ {
  id: string;
  question: string;
  answer: string;
}

const FAQContent: React.FC = () => {
  const { data, isPending, error } = useFaq();
  const { locale } = useParams();

  const faqs: FAQ[] =
    data?.data?.items?.map((item: any) => ({
      id: item.id,
      question: locale === "ar" ? item.questionAr : item.questionEn,
      answer: locale === "ar" ? item.answerAr : item.answerEn,
    })) ?? [];

  if (isPending) {
    return (
      <Card className="shadow-soft from-background to-brand-warm/20 border-0 bg-gradient-to-br">
        <CardContent className="p-8">
          <div className="flex items-center justify-center py-12">
            <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
            <span className="text-muted-foreground ml-3">Loading FAQs...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="shadow-soft from-background to-brand-warm/20 border-0 bg-gradient-to-br">
        <CardContent className="p-8">
          <div className="py-12 text-center">
            <p className="text-muted-foreground">
              Unable to load FAQs. Please try again later.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (faqs.length === 0) {
    return (
      <Card className="shadow-soft from-background to-brand-warm/20 border-0 bg-gradient-to-br">
        <CardContent className="p-8">
          <div className="py-12 text-center">
            <p className="text-muted-foreground">
              No FAQs available at the moment.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-soft from-background to-brand-warm/20 border-0 bg-gradient-to-br">
      <CardContent className="p-6">
        <Accordion type="single" collapsible className="w-full space-y-4">
          {faqs.map((faq, index) => (
            <AccordionItem
              key={faq.id}
              value={faq.id}
              className="bg-background/50 hover:bg-background/80 rounded-xl border px-6 transition-colors duration-300"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <AccordionTrigger className="text-foreground hover:text-primary text-left font-semibold transition-colors duration-300">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-muted-foreground pt-2 leading-relaxed">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
};

export default FAQContent;
