"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { FloatingInput } from "@/components/ui/floating-input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { useCreateContact } from "@/hooks/contact";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import FormErrors from "@/components/FormErrors";
import FormFeedback from "@/components/FormFeedback";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { contactRequestReasons } from "@/constants/contactRequestReasons";
import {
  Mail,
  MessageSquare,
  Phone,
  Send,
  TextSelectIcon,
  User,
} from "lucide-react";
import { createTranslator, useLocale, useTranslations } from "next-intl";
import { useMemo, useState } from "react";
import messages from "../../../../../../messages/en.json";

function createContactFormSchema(t: ReturnType<typeof createTranslator>) {
  return z.object({
    fullName: z
      .string()
      .min(1, t("validations.required"))
      .max(100, t("validations.maxLength", { max: 100 })),

    email: z
      .string()
      .min(1, t("validations.required"))
      .email(t("validations.email")),

    phone: z
      .string()
      .min(1, t("validations.required"))
      .regex(
        /^[0-9]{10,15}$/,
        t("validations.phoneDigitsRange", { min: 10, max: 15 }),
      ),

    reason: z.number().min(0, {
      message: "Please select a reason for contact",
    }),

    message: z
      .string()
      .min(5, t("validations.minLength", { min: 5 }))
      .max(1000, t("validations.maxLength", { max: 1000 })),
  });
}

type ContactFormSchema = ReturnType<typeof createContactFormSchema>;
type ContactFormValues = z.infer<ContactFormSchema>;

export default function ContactForm() {
  const t = useTranslations();
  const locale = useLocale();
  const isRTL = locale === "ar";
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    mutate: createContact,
    isPending,
    error,
    isError,
    reset,
  } = useCreateContact();

  const formSchema = useMemo(() => createContactFormSchema(t), [t]);

  const form = useForm<ContactFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      reason: undefined,
      message: "",
    },
  });

  function onSubmit(values: ContactFormValues) {
    createContact(values, {
      onSuccess: () => {
        form.reset();
        reset();
        setIsSuccess(true);
      },
    });
  }

  return (
    <>
      <div>
        <div className="mb-8 flex flex-col items-center gap-6 text-center">
          <div className="bg-primary rounded-2xl p-4 text-white">
            <Send className="h-18 w-18" />
          </div>
          <div className="grid gap-2">
            <h1 className="text-3xl">{t("contact.form.title")}</h1>
            <p>{t("contact.form.description")}</p>
          </div>
        </div>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 gap-8">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        label={t("contact.form.name")}
                        icon={User}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        type="tel"
                        label={t("contact.form.email")}
                        icon={Mail}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        type="tel"
                        label={t("contact.form.phone")}
                        icon={Phone}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reason"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <div className="relative">
                        <div className="p- relative">
                          {/* Icon */}
                          <TextSelectIcon
                            className={`text-primary absolute top-3 ${isRTL ? "right-3" : "left-3"} h-4 w-4`}
                          />

                          {/* Select */}
                          <Select
                            value={field.value?.toString()}
                            onValueChange={(value) =>
                              field.onChange(parseInt(value))
                            }
                          >
                            <SelectTrigger
                              className={`border-input focus:border-primary focus:ring-primary/20 h-auto w-full py-3 ${isRTL ? "flex-row-reverse pr-10" : "pl-10"} flex focus:ring-2`}
                            >
                              <SelectValue className="flex flex-row-reverse" />
                            </SelectTrigger>
                            <SelectContent>
                              {contactRequestReasons.map((reason) => {
                                const key = reason.label.split(".").at(-1);
                                const label = t(
                                  `contact.form.reasons.${key as keyof typeof messages.contact.form.reasons}`,
                                );

                                return (
                                  <SelectItem
                                    className="p-2"
                                    key={reason.value}
                                    value={reason.value.toString()}
                                  >
                                    {label}
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>

                          {/* Floating Label */}
                          <label
                            className={`text-primary bg-background pointer-events-none absolute -top-2 ${isRTL ? "right-10" : "left-10"} px-1 text-xs`}
                          >
                            {t("contact.form.reason")}
                          </label>
                        </div>

                        {/* Error Message */}
                        {fieldState.error && (
                          <p className="text-destructive mt-1 text-xs">
                            {fieldState.error.message}
                          </p>
                        )}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="message"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <FloatingInput
                      className=""
                      type="textarea"
                      label={t("contact.form.message")}
                      icon={MessageSquare}
                      error={fieldState.error?.message}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {isError && <FormErrors error={error} />}

            <Button className="h-12 w-full" type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                  {t("contact.form.sending")}
                </>
              ) : (
                <>
                  <Send className="mr-2 h-10 w-10" />
                  {t("contact.form.submit")}
                </>
              )}
            </Button>
          </form>
        </Form>

        <FormFeedback open={isSuccess} onOpenChange={setIsSuccess} />
      </div>
    </>
  );
}
