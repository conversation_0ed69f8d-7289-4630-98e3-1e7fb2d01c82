import api from "@/lib/axios";
import { isAxiosError } from "axios";
import { DefaultStatus } from "../types/global";

export const getContactRequests = async ({
  pageNumber = 1,
  pageSize = 10,
  status,
}: {
  pageNumber?: number;
  pageSize?: number;
  status?: DefaultStatus;
}) => {
  try {
    const res = await api.get("/contact-requests", {
      params: {
        pageNumber,
        pageSize,
        ...(status !== undefined ? { status } : {}),
      },
    });

    return res.data;
  } catch (err) {
    console.error(err);

    if (isAxiosError(err)) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};

export const updateContactRequestStatus = async (
  id: string,
  status: DefaultStatus,
) => {
  try {
    const res = await api.patch(`/contact-requests/${id}`, { status });
    return res.data;
  } catch (err) {
    if (isAxiosError(err)) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
      }

      if (err.response?.status === 404) {
        throw new Error("طلب الاتصال غير موجود");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};

export const deleteContactRequest = async (id: string) => {
  try {
    const res = await api.delete(`/contact-requests/${id}`);
    return res.data;
  } catch (err) {
    console.error(err);

    if (isAxiosError(err)) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401) {
        throw new Error("غير مصرح به - مطلوب دور المسؤول (401)");
      }

      if (err.response?.status === 403) {
        throw new Error("(403) ممنوع - أذونات غير كافية");
      }

      if (err.response?.status === 404) {
        throw new Error("(404) غير موجود");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};
