"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { useLocale } from "next-intl";

export interface FloatingInputProps extends React.ComponentProps<"input"> {
  label: string;
  icon?: LucideIcon;
  error?: string;
}

const FloatingInput = React.forwardRef<HTMLInputElement, FloatingInputProps>(
  ({ className, type = "text", label, icon: Icon, error, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(false);
    const locale = useLocale();

    const handleFocus = () => setIsFocused(true);
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      setHasValue(e.target.value.length > 0);
      props.onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e);
    };

    const isLabelFloating = isFocused || hasValue;
    const isRTL = locale === "ar";

    return (
      <div className="relative">
        <div className="relative">
          {/* Icon */}
          {Icon && (
            <Icon
              className={cn(
                "absolute h-4 w-4 transition-all duration-200 ease-in-out",
                isRTL ? "right-3" : "left-3",
                isLabelFloating
                  ? "text-primary top-3"
                  : "text-muted-foreground top-1/2 -translate-y-1/2",
              )}
            />
          )}

          {/* Input */}
          <input
            type={type}
            ref={ref}
            className={cn(
              "peer border-input w-full rounded-md border bg-transparent px-3 py-3 text-base placeholder-transparent transition-all duration-200 ease-in-out outline-none",
              "focus:border-primary focus:ring-primary/20 focus:ring-2",
              Icon && (isRTL ? "pr-10" : "pl-10"),
              error &&
                "border-destructive focus:border-destructive focus:ring-destructive/20",
              className,
            )}
            placeholder={label}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />

          {/* Floating Label */}
          <label
            className={cn(
              "pointer-events-none absolute transition-all duration-200 ease-in-out",
              isRTL ? "right-3" : "left-3",
              Icon && (isRTL ? "right-10" : "left-10"),
              isLabelFloating
                ? "bg-background text-primary -top-2 px-1 text-xs"
                : "text-muted-foreground top-1/2 -translate-y-1/2 text-base",
            )}
          >
            {label}
          </label>
        </div>

        {/* Error Message */}
        {error && <p className="text-destructive mt-1 text-xs">{error}</p>}
      </div>
    );
  },
);

FloatingInput.displayName = "FloatingInput";

export { FloatingInput };
