import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { updateInvestorRequestStatus } from "../../services/investorRequests";
import { DefaultStatus } from "../../types/global";

export default function useUpdateInvestorRequestStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: DefaultStatus }) =>
      updateInvestorRequestStatus(id, status),
    onSuccess: () => {
      toast.success("تم تحديث حالة طلب المستثمر");
      queryClient.invalidateQueries({
        queryKey: ["investor-requests"],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
}
