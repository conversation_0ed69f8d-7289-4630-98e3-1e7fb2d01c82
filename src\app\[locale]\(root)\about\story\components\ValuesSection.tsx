import { Card, CardContent } from "@/components/ui/card";
import { Globe, Heart, Shield, Sparkles, Users } from "lucide-react";
import { getTranslations } from "next-intl/server";

interface Value {
  icon: React.ElementType;
  title: string;
  description: string;
}

const ValuesSection: React.FC = async () => {
  const t = await getTranslations();

  const values: Value[] = [
    {
      icon: Heart,
      title: t("about.values.excellence.title"),
      description: t("about.values.excellence.description"),
    },
    {
      icon: Shield,
      title: t("about.values.trust.title"),
      description: t("about.values.trust.description"),
    },
    {
      icon: Globe,
      title: t("about.values.innovation.title"),
      description: t("about.values.innovation.description"),
    },
    {
      icon: Users,
      title: t("about.values.community.title"),
      description: t("about.values.community.description"),
    },
  ];

  return (
    <section className="from-brand-teal-dark/15 to-primary/10 via-background bg-gradient-to-br py-20">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-6 py-3">
            <Sparkles className="text-primary me-2 size-4 md:size-5" />
            <span className="text-primary text-sm font-semibold md:text-base lg:text-lg">
              {t("about.values.subtitle")}
            </span>
          </div>

          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("about.values.title")}
          </h2>

          <p className="text-muted-foreground mx-auto max-w-2xl text-lg md:text-xl lg:text-2xl">
            {t("about.values.description")}
          </p>
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {values.map((value, index) => (
            <Card
              key={index}
              className="shadow-soft from-card/50 via-brand-teal-dark/10 to-brand-teal-dark/5 hover:shadow-elevated border-border/50 border-2 bg-gradient-to-br text-center transition-all duration-300"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="flex flex-col items-center px-6 py-4">
                <div className="bg-primary/10 text-primary mb-6 inline-flex h-16 w-16 items-center justify-center rounded-full">
                  <value.icon className="h-8 w-8" />
                </div>

                <h3 className="text-primary mb-4 text-xl font-bold md:text-2xl">
                  {value.title}
                </h3>

                <p className="text-muted-foreground leading-relaxed">
                  {value.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ValuesSection;
