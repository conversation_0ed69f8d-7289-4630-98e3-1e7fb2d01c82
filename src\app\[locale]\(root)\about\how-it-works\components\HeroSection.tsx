import { Badge } from "@/components/ui/badge";
import { CheckCircle } from "lucide-react";
import { getTranslations } from "next-intl/server";

const HeroSection: React.FC = async () => {
  const t = await getTranslations("howItWorks");

  return (
    <section className="relative overflow-hidden py-20 lg:py-32">
      <div className="from-primary/15 via-primary/5 to-primary/5 absolute inset-0 bg-gradient-to-br" />

      <div className="relative mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <Badge className="border-border bg-card/40 mb-6 rounded-full border-2 px-6 py-2 font-semibold transition-all duration-300">
            <CheckCircle className="text-primary !size-4 md:!size-5" />
            <span className="text-primary text-sm font-semibold md:text-base">
              {t("badge")}
            </span>
          </Badge>
          <h1 className="text-foreground mb-6 text-4xl leading-tight font-bold md:text-5xl lg:text-6xl">
            {t("title")}
          </h1>
          <p className="text-muted-foreground mx-auto mb-4 max-w-3xl text-xl leading-relaxed font-semibold md:text-2xl">
            {t("subtitle")}
          </p>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed font-semibold md:text-xl">
            {t("description")}
          </p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
