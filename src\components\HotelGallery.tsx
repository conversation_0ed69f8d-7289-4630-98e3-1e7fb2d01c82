import { ArrowRight, MapPin } from "lucide-react";
import { useTranslations } from "next-intl";

const CitiesSection = () => {
  const t = useTranslations();

  const cityItems = [
    {
      id: 1,
      nameKey: "riyadh",
      titleKey: "cities.riyadh",
      image:
        "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.riyadh",
      unitsCount: 45,
    },
    {
      id: 2,
      nameKey: "jeddah",
      titleKey: "cities.jeddah",
      image:
        "https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.jeddah",
      unitsCount: 32,
    },
    {
      id: 3,
      nameKey: "dammam",
      titleKey: "cities.dammam",
      image:
        "https://images.unsplash.com/photo-1512453979798-5ea266f8880c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.dammam",
      unitsCount: 18,
    },
    {
      id: 4,
      nameKey: "mecca",
      titleKey: "cities.mecca",
      image:
        "https://images.unsplash.com/photo-1591604129939-f1efa4d9f7fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.mecca",
      unitsCount: 28,
    },
    {
      id: 5,
      nameKey: "medina",
      titleKey: "cities.medina",
      image:
        "https://images.unsplash.com/photo-1564769625392-651b2c0e7b8a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.medina",
      unitsCount: 22,
    },
    {
      id: 6,
      nameKey: "khobar",
      titleKey: "cities.khobar",
      image:
        "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.khobar",
      unitsCount: 15,
    },
    {
      id: 7,
      nameKey: "taif",
      titleKey: "cities.taif",
      image:
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.taif",
      unitsCount: 12,
    },
    {
      id: 8,
      nameKey: "abha",
      titleKey: "cities.abha",
      image:
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      altKey: "cities.abha",
      unitsCount: 8,
    },
  ];

  return (
    <section className="px-4 py-20 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="mb-16 text-center">
          <h2 className="text-primary mb-4 text-4xl font-bold md:text-5xl">
            {t("cities.title")}
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-gray-600">
            {t("cities.description")}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {cityItems.map((item, index) => (
            <div key={index} className="group cursor-pointer">
              <div className="relative transform overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl">
                <div className="aspect-w-4 aspect-h-5">
                  <img
                    alt={t(item.altKey)}
                    className="h-80 w-full object-cover transition-transform duration-500 group-hover:scale-110"
                    src={item.image}
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
                <div className="absolute right-0 bottom-0 left-0 translate-y-4 transform p-6 text-white transition-transform duration-300 group-hover:translate-y-0">
                  <h3 className="mb-2 text-xl font-semibold">
                    {t(`cities.${item.nameKey}`)}
                  </h3>
                  <p className="mb-4 text-sm text-white/80">
                    {item.unitsCount}+ {t("cities.buildings")}
                  </p>
                  <div className="flex items-center gap-3 opacity-0 transition-all delay-100 duration-300 group-hover:opacity-100">
                    <button className="group/button inline-flex transform items-center gap-2 rounded-full border border-white/30 bg-white/20 px-4 py-2.5 text-sm font-medium text-white shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:border-white/50 hover:bg-white/30 hover:shadow-xl active:scale-95">
                      <MapPin className="h-4 w-4 opacity-80" />
                      <span>{t("cities.viewUnits")}</span>
                      <ArrowRight className="h-4 w-4 transform transition-transform duration-300 group-hover/button:translate-x-1" />
                    </button>
                    <div className="h-2 w-2 animate-pulse rounded-full bg-white/60"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CitiesSection;
