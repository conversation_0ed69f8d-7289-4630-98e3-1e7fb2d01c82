import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Eye, Pencil } from "lucide-react";
import {
  createSelectColumn,
  createTextColumn,
} from "../../components/data-table";
import {
  CorporateRequest,
  CorporateRequestStatus,
} from "../../types/corporateRequests";
import { statusClasses, statusLabels } from "./CorporateFilterByStatus";
import DeleteCorporateRequestButton from "./DeleteCorporateRequestButton";
import UpdateCorporateRequestStatusButton from "./UpdateCorporateRequestStatusButton";

export const corporateRequestsColumns: ColumnDef<CorporateRequest>[] = [
  // createSelectColumn<CorporateRequest>(),
  createTextColumn<CorporateRequest>("fullName", "الاسم الكامل"),
  createTextColumn<CorporateRequest>("email", "البريد الإلكتروني"),
  createTextColumn<CorporateRequest>("phone", "رقم الهاتف"),
  createTextColumn<CorporateRequest>("company", "الشركة"),
  createTextColumn<CorporateRequest>("websiteOrLinkedIn", "الموقع/لينكدإن", {
    sortable: false,
  }),
  {
    accessorKey: "message",
    meta: { arabicName: "الرسالة" },
    header: "الرسالة",
    cell: ({ row }) => {
      const message = (row.getValue("message") as string) || "";
      const max = 40;
      const isLong = message.length > max;
      const preview = isLong ? message.slice(0, max) + "…" : message;

      return (
        <div className="flex max-w-[200px] min-w-[150px] items-center gap-2 overflow-hidden text-wrap whitespace-pre-wrap">
          <span className="text-foreground/80 text-sm">{preview}</span>
          {isLong && (
            <Popover>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PopoverTrigger asChild>
                    <Button
                      aria-label="عرض الرسالة كاملة"
                      variant="secondary"
                      size="icon"
                      className="rounded-full"
                    >
                      <Eye className="size-4" />
                      <span className="sr-only">عرض الرسالة</span>
                    </Button>
                  </PopoverTrigger>
                </TooltipTrigger>
                <TooltipContent variant="secondary">عرض الرسالة</TooltipContent>
              </Tooltip>
              <PopoverContent
                align="end"
                side="top"
                className="max-w-md text-sm leading-6 whitespace-pre-wrap"
              >
                {message}
              </PopoverContent>
            </Popover>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    meta: { arabicName: "الحالة" },
    header: "الحالة",
    cell: ({ row }) => {
      const status = row.getValue("status") as CorporateRequestStatus;

      return (
        <span
          className={`rounded-full px-2 py-1 text-xs font-medium ${statusClasses[status]}`}
        >
          {statusLabels[status]}
        </span>
      );
    },
  },
  // Optionally show created at (remove if you don't want it)
  createTextColumn<CorporateRequest>("createdAt", "تاريخ الإنشاء", {
    sortable: true,
    // You can format if needed:
    format(value) {
      return format(value, "yyyy-MM-dd HH:mm:ss");
    },
  }),
  {
    header: "الإجراءات",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-1.5">
          {/* <Tooltip>
            <TooltipTrigger asChild>
              <Button
                aria-label="عرض"
                variant="secondary"
                size="icon"
                className="rounded-full"
                onClick={() => console.log("view", row.original)}
              >
                <Eye className="size-4" />
                <span className="sr-only">عرض</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent variant="secondary">عرض</TooltipContent>
          </Tooltip> */}

          <UpdateCorporateRequestStatusButton
            currentStatus={row.original.status}
            id={row.original.id}
          />

          <DeleteCorporateRequestButton id={row.original.id} />
        </div>
      );
    },
  },
];
