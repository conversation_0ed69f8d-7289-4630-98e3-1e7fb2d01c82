import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import { getTranslations } from "next-intl/server";

interface AccommodationType {
  title: string;
  description: string;
  image: string;
  features: string[];
}

const AccommodationTypes: React.FC = async () => {
  const t = await getTranslations("corporate");

  const accommodationTypes: AccommodationType[] = [
    {
      title: t("accommodation.types.studio.title"),
      description: t("accommodation.types.studio.description"),
      image: "/CorporateStay/studio.jpg",
      features: [
        t("accommodation.features.oneBedroom"),
        t("accommodation.features.workDesk"),
        t("accommodation.features.kitchenette"),
        t("accommodation.features.highSpeedWifi"),
      ],
    },
    {
      title: t("accommodation.types.multi.title"),
      description: t("accommodation.types.multi.description"),
      image: "/CorporateStay/multi.jpg",
      features: [
        t("accommodation.features.twoToFourBedrooms"),
        t("accommodation.features.sharedWorkspace"),
        t("accommodation.features.fullKitchen"),
        t("accommodation.features.livingArea"),
      ],
    },
    {
      title: t("accommodation.types.entire.title"),
      description: t("accommodation.types.entire.description"),
      image: "/CorporateStay/entire_floor.webp",
      features: [
        t("accommodation.features.multipleUnits"),
        t("accommodation.features.conferenceRoom"),
        t("accommodation.features.flexibleLayout"),
        t("accommodation.features.dedicatedSupport"),
      ],
    },
  ];

  return (
    <section className="bg-background py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("accommodation.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
            {t("accommodation.description")}
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {accommodationTypes.map((type, index) => (
            <Card
              key={index}
              className="group bg-card text-card-foreground transform border p-0 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl"
            >
              <div className="relative overflow-hidden rounded-t-lg">
                <img
                  src={type.image}
                  alt={type.title}
                  className="h-48 w-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              </div>
              <CardContent className="p-6">
                <h3 className="text-foreground mb-3 text-xl font-bold">
                  {type.title}
                </h3>
                <p className="text-muted-foreground mb-4">{type.description}</p>
                <div className="space-y-2">
                  {type.features.map((feature, featureIndex) => (
                    <div
                      key={featureIndex}
                      className="text-muted-foreground flex items-center text-sm"
                    >
                      <CheckCircle className="me-2 h-4 w-4 flex-shrink-0 text-[#279fc7]" />
                      {feature}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default AccommodationTypes;
