import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ColumnDef } from "@tanstack/react-table";
import { Eye } from "lucide-react";
import {
  createSelectColumn,
  createTextColumn,
} from "../../components/data-table";
import {
  defaultStatusClasses,
  defaultStatusLabels,
} from "../../components/GenericStatusFilter";
import { formatDate } from "../../lib/formatters";
import { DefaultStatus } from "../../types/global";
import { InvestorRequest } from "../../types/investorRequests";
import DeleteInvestorRequestButton from "./DeleteInvestorRequestButton";
import UpdateInvestorRequestStatusButton from "./UpdateInvestorRequestStatusButton";

export const investorRequestsColumns: ColumnDef<InvestorRequest>[] = [
  // createSelectColumn<InvestorRequest>(),
  createTextColumn<InvestorRequest>("fullName", "الاسم الكامل"),
  createTextColumn<InvestorRequest>("email", "البريد الإلكتروني"),
  createTextColumn<InvestorRequest>("phone", "رقم الهاتف"),
  createTextColumn<InvestorRequest>("propertyLocation", "موقع العقار"),
  {
    accessorKey: "message",
    meta: { arabicName: "الرسالة" },
    header: "الرسالة",
    cell: ({ row }) => {
      const message = (row.getValue("message") as string) || "";
      const max = 40;
      const isLong = message.length > max;
      const preview = isLong ? message.slice(0, max) + "…" : message;

      return (
        <div className="flex max-w-[200px] min-w-[150px] items-center gap-2 overflow-hidden text-wrap whitespace-pre-wrap">
          <span className="text-foreground/80 text-sm">{preview}</span>
          {isLong && (
            <Popover>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PopoverTrigger asChild>
                    <Button
                      aria-label="عرض الرسالة كاملة"
                      variant="secondary"
                      size="icon"
                      className="rounded-full"
                    >
                      <Eye className="size-4" />
                      <span className="sr-only">عرض الرسالة</span>
                    </Button>
                  </PopoverTrigger>
                </TooltipTrigger>
                <TooltipContent variant="secondary">عرض الرسالة</TooltipContent>
              </Tooltip>
              <PopoverContent
                align="end"
                side="top"
                className="max-w-md text-sm leading-6 whitespace-pre-wrap"
              >
                {message}
              </PopoverContent>
            </Popover>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    meta: { arabicName: "الحالة" },
    header: "الحالة",
    cell: ({ row }) => {
      const status = row.getValue("status") as DefaultStatus;

      return (
        <span
          className={`rounded-full px-2 py-1 text-xs font-medium ${defaultStatusClasses[status]}`}
        >
          {defaultStatusLabels[status]}
        </span>
      );
    },
  },
  createTextColumn<InvestorRequest>("createdAt", "تاريخ الإنشاء", {
    sortable: true,
    format(value) {
      return formatDate(new Date(value));
    },
  }),
  {
    id: "actions",
    header: "الإجراءات",
    cell: ({ row }) => {
      const request = row.original;
      return (
        <div className="flex items-center justify-start gap-1.5">
          <UpdateInvestorRequestStatusButton
            id={request.id}
            currentStatus={request.status}
          />
          <DeleteInvestorRequestButton id={request.id} />
        </div>
      );
    },
  },
];
