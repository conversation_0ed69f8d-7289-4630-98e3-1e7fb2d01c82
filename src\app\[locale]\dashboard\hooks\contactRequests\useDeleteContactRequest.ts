import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { deleteContactRequest } from "../../services/contactRequests";

export default function useDeleteContactRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteContactRequest(id),
    onSuccess: () => {
      toast.success("تم حذف طلب الاتصال بنجاح.");
      queryClient.invalidateQueries({
        queryKey: ["contact-requests"],
      });
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
