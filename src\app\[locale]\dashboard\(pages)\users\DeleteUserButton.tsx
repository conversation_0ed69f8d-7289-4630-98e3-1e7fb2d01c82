import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Trash2 } from "lucide-react";
import useDeleteUser from "../../hooks/users/useDeleteUser";
import { useState } from "react";
import { ConfirmDialog } from "../../components/ConfirmDialog";

export default function DeleteUserButton({ id }: { id: string }) {
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const { mutate, isPending } = useDeleteUser();

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label="حذف"
            variant="destructive"
            size="icon"
            className="rounded-full"
            onClick={() => setIsConfirmDialogOpen(true)}
          >
            <Trash2 className="size-4" />
            <span className="sr-only">حذف</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent variant="destructive">حذف</TooltipContent>
      </Tooltip>
      {isConfirmDialogOpen && (
        <ConfirmDialog
          destructive
          isLoading={isPending}
          title="حذف المستخدم"
          description="هل أنت متأكد من حذف هذا المستخدم؟"
          confirmText="حذف"
          cancelText="إلغاء"
          onCancelAction={() => setIsConfirmDialogOpen(false)}
          onConfirmAction={() => {
            mutate(id, {
              onSuccess: () => setIsConfirmDialogOpen(false),
            });
          }}
        />
      )}
    </>
  );
}
