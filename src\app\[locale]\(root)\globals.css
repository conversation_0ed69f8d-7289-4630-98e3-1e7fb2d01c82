@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --color-brand-teal: var(--brand-teal);
  --color-brand-teal-light: var(--brand-teal-light);
  --color-brand-teal-dark: var(--brand-teal-dark);
  --color-brand-ocean: var(--brand-ocean);
  --color-brand-warm: var(--brand-warm);
}

:root {
  --primary: #279fc7;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  --background: oklch(1 0 0);
  --foreground: oklch(0.26 0.04 248);

  --card: oklch(1 0 0);
  --card-foreground: oklch(0.26 0.04 248);

  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.26 0.04 248);

  --primary: oklch(0.56 0.16 213);
  --primary-foreground: oklch(1 0 0);

  --secondary: oklch(0.97 0.02 213);
  --secondary-foreground: oklch(0.26 0.04 248);

  --muted: oklch(0.97 0.02 213);
  --muted-foreground: oklch(46.814% 0.02214 243.409);

  --accent: oklch(0.91 0.09 213);
  --accent-foreground: oklch(0.26 0.04 248);

  --destructive: oklch(0.63 0.24 29);
  --destructive-foreground: oklch(0.98 0.01 248);

  --border: oklch(66.99% 0.08281 216.48);
  --input: oklch(0.97 0.01 213);
  --ring: oklch(0.56 0.16 213);

  --radius: 0.75rem;

  /* Hala Brand Colors */
  --brand-teal: oklch(0.56 0.16 213);
  --brand-teal-light: oklch(67.48% 0.12024 226.154);
  --brand-teal-dark: oklch(50.482% 0.12501 226.048);
  --brand-ocean: oklch(73% 0.08 245);
  --brand-warm: oklch(96% 0.02 95);
}

.dark {
  --primary: #279fc7;
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);

  --background: oklch(0.19 0.03 248);
  --foreground: oklch(0.98 0 0);

  --card: oklch(0.23 0.02 248);
  --card-foreground: oklch(0.98 0 0);

  --popover: oklch(0.23 0.02 248);
  --popover-foreground: oklch(0.98 0 0);

  --primary: #279fc7;
  --primary-foreground: oklch(0.26 0.03 248);

  --secondary: oklch(0.28 0.02 248);
  --secondary-foreground: oklch(0.9 0 0);

  --muted: oklch(0.24 0.02 248);
  --muted-foreground: oklch(69.932% 0.02308 246.065);

  --accent: oklch(0.33 0.05 213);
  --accent-foreground: oklch(0.9 0 0);

  --destructive: oklch(0.6 0.23 29);
  --destructive-foreground: oklch(0.98 0 0);

  --border: oklch(44.041% 0.04209 247.596);
  --input: oklch(0.28 0.02 248);
  --ring: oklch(0.78 0.14 213);
}

/* Custom keyframes */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

/* Animation utilities */
@utility accordion-down {
  animation: var(--animate-accordion-down);
}

@utility accordion-up {
  animation: var(--animate-accordion-up);
}

/* Background gradient utilities */
@utility gradient-ocean {
  background-image: var(--gradient-ocean);
}

@utility gradient-warm {
  background-image: var(--gradient-warm);
}

/* Shadow utilities */
@utility shadow-soft {
  box-shadow: var(--shadow-soft);
}

@utility shadow-elevated {
  box-shadow: var(--shadow-elevated);
}

/* Transition utilities */
@utility transition-smooth {
  transition: var(--transition-smooth);
}

/* Border radius utilities */
@utility rounded-lg {
  border-radius: var(--radius-lg);
}

@utility rounded-md {
  border-radius: var(--radius-md);
}

@utility rounded-sm {
  border-radius: var(--radius-sm);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-almarai), Arial;
  }
}

@layer utilities {
  .container {
    @apply mx-auto px-4 xl:max-w-6xl 2xl:max-w-7xl;
  }

  :is(button, [role="button"], [role="a"], [role="menuitem"], a):not(
    :disabled
  ) {
    @apply cursor-pointer;

    &:hover svg {
      @apply [transform:rotate(360deg)] transition-transform duration-400 ease-in-out;
    }
  }
}
