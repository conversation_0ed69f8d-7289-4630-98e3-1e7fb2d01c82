"use client";

import React from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export type PageSizeSelectProps = {
  paramName?: string; // URL param key, defaults to "pageSize"
  values?: number[]; // Allowed page sizes
  resetPageParam?: string; // param to reset when size changes, defaults to "pageNumber"
  placeholder?: string; // select placeholder
  align?: "start" | "center" | "end"; // dropdown alignment
  className?: string;
  label?: string; // label to show before the select
  totalPages?: number; // used to make options dynamic based on dataset size
};

export default function PageSizeSelect({
  paramName = "pageSize",
  values = [10, 15, 25, 50, 100],
  resetPageParam = "pageNumber",
  placeholder = "عدد الصفوف",
  align = "end",
  className,
  label = "عدد الصفوف:",
  totalPages,
}: PageSizeSelectProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const current = searchParams.get(paramName);
  const currentValue =
    current && !Number.isNaN(Number(current)) ? current : undefined;
  const currentValueNum = currentValue ? Number(currentValue) : values[0];

  // If totalPages is provided, limit available options based on an approximate
  // total item count (totalPages * currentPageSize). Always include the current value.
  const maxItemsApprox = totalPages ? totalPages * currentValueNum : undefined;
  let dynamicValues = values.filter((v) =>
    maxItemsApprox ? v <= maxItemsApprox : true,
  );
  if (!dynamicValues.includes(currentValueNum)) {
    dynamicValues = [...dynamicValues, currentValueNum];
  }
  dynamicValues = Array.from(new Set(dynamicValues)).sort((a, b) => a - b);

  return (
    <div className="flex items-center gap-2">
      {label && (
        <span className="text-muted-foreground text-sm whitespace-nowrap">
          {label}
        </span>
      )}
      <Select
        value={currentValue ?? undefined}
        onValueChange={(v) => {
          const params = new URLSearchParams(searchParams.toString());
          params.set(paramName, v);
          // Reset page number to 1 if applicable
          if (resetPageParam) params.set(resetPageParam, "1");
          router.push(`${pathname}?${params.toString()}`, { scroll: false });
        }}
      >
        <SelectTrigger className={className}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent align={align}>
          {dynamicValues.map((v) => (
            <SelectItem key={v} value={String(v)}>
              {v}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
