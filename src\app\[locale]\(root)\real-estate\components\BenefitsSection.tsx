import { Card } from "@/components/ui/card";
import { TrendingUp, <PERSON><PERSON>he<PERSON>, DollarSign } from "lucide-react";
import { getTranslations } from "next-intl/server";

interface Benefit {
  icon: React.ElementType;
  title: string;
  description: string;
}

const BenefitsSection: React.FC = async () => {
  const t = await getTranslations("realEstate.benefits");

  const benefits: Benefit[] = [
    {
      icon: TrendingUp,
      title: t("items.increase.title"),
      description: t("items.increase.description"),
    },
    {
      icon: ShieldCheck,
      title: t("items.protection.title"),
      description: t("items.protection.description"),
    },
    {
      icon: DollarSign,
      title: t("items.guaranteed.title"),
      description: t("items.guaranteed.description"),
    },
  ];

  return (
    <section className="bg-muted/30 py-20">
      <div className="container mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold md:text-4xl">{t("title")}</h2>
          <p className="text-muted-foreground mt-2 text-lg">{t("subtitle")}</p>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {benefits.map((benefit, index) => (
            <Card
              key={index}
              className="bg-card text-card-foreground transform border p-10 text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl"
            >
              <div className="bg-secondary border-muted-foreground/20 mx-auto flex h-16 w-16 items-center justify-center rounded-full border-2">
                <benefit.icon className="text-primary h-8 w-8" />
              </div>
              <h3 className="text-primary mb-2 text-xl font-semibold">
                {benefit.title}
              </h3>
              <p className="text-secondary-foreground/80">
                {benefit.description}
              </p>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
