import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { getUsers } from "../../services/users";
import { UserStatus } from "../../types/users";

const useGetUsersQuery = () => {
  const searchParams = useSearchParams();
  const pageNumber = Number(searchParams.get("pageNumber")) || 1;
  const pageSize = Number(searchParams.get("pageSize")) || 10;
  const status =
    UserStatus[searchParams.get("status") as keyof typeof UserStatus] ||
    undefined;

  return useQuery({
    queryKey: ["users", pageNumber, pageSize, status],
    queryFn: () =>
      getUsers({
        pageNumber,
        pageSize,
        ...(status !== undefined ? { status } : {}),
      }),
    staleTime: 1000 * 60 * 2, // 2 minutes
    retry: 3,
    retryDelay: 1000,
  });
};

export default useGetUsersQuery;
