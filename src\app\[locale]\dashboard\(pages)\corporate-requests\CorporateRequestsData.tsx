"use client";

import GenericPageLoading from "@/components/GenericPageLoading";
import ApiError from "../../components/ApiError";
import { DataTable } from "../../components/data-table";
import GenericStatusFilter from "../../components/GenericStatusFilter";
import useGetCorporateRequests from "../../hooks/corporateRequests/useGetCorporateRequests";
import { corporateRequestsColumns } from "./CorporateRequestsColumns";

export default function CorporateRequestsData() {
  const { data, isLoading, error } = useGetCorporateRequests();

  if (isLoading) {
    return <GenericPageLoading />;
  }

  if (error || !data) {
    return <ApiError error={error as Error} />;
  }

  return (
    <div>
      <DataTable
        columns={corporateRequestsColumns}
        data={data.items}
        pagination={{
          pageNumber: data.pageNumber,
          totalPages: data.totalPages,
          hasPreviousPage: data.hasPreviousPage,
          hasNextPage: data.hasNextPage,
        }}
        beforeTable={<GenericStatusFilter />}
      />
    </div>
  );
}
