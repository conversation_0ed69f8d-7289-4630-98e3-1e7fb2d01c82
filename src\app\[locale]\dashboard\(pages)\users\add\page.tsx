import { But<PERSON> } from "@/components/ui/button";
import PageHeading from "../../../components/PageHeading";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

export default function AddNewUserPage() {
  return (
    <div className="space-y-10">
      <div className="flex justify-between gap-2">
        <PageHeading>إضافة مستخدم جديد</PageHeading>
        <Button asChild className="self-end">
          <Link href={"/dashboard/users"}>
            رجوع للمستخدمين
            <ArrowRight />
          </Link>
        </Button>
      </div>
    </div>
  );
}
