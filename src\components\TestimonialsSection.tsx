"use client";

import { useTranslations } from "next-intl";
import { useLanguage } from "@/providers/LanguageContext";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, Heart, MapPin, X } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

const TestimonialsSection = () => {
  const t = useTranslations();
  const { isRTL } = useLanguage();
  const { toast } = useToast();
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedRating, setSelectedRating] = useState(0);
  const [reviewText, setReviewText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      location: "New York, NY",
      avatar: "SC",
      rating: 5,
      textKey: "testimonials.sarah.text",
      verified: true,
      stayDuration: "3 nights",
      property: "Dubai Marina Penthouse",
    },
    {
      id: 2,
      name: "Marcus <PERSON>",
      location: "London, UK",
      avatar: "MJ",
      rating: 5,
      textKey: "testimonials.marcus.text",
      verified: true,
      stayDuration: "1 week",
      property: "NYC Central Park View",
    },
    {
      id: 3,
      name: "Elena Rodriguez",
      location: "Barcelona, Spain",
      avatar: "ER",
      rating: 5,
      textKey: "testimonials.elena.text",
      verified: true,
      stayDuration: "5 nights",
      property: "London Thames Apartment",
    },
  ];

  const handleSubmitReview = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedRating === 0) {
      toast({
        title: t("testimonials.addReview.error.title", "Rating Required"),
        description: t(
          "testimonials.addReview.error.ratingRequired",
          "Please select a rating before submitting your review.",
        ),
        variant: "destructive",
      });
      return;
    }

    if (!reviewText.trim()) {
      toast({
        title: t("testimonials.addReview.error.title", "Review Required"),
        description: t(
          "testimonials.addReview.error.reviewRequired",
          "Please write your review before submitting.",
        ),
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call - replace with actual review submission
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Show success message
      toast({
        title: t("testimonials.addReview.success.title", "Review Submitted!"),
        description: t(
          "testimonials.addReview.success.description",
          "Thank you for sharing your experience. Your review has been submitted successfully.",
        ),
        variant: "success",
      });

      // Reset form and close modal
      setReviewText("");
      setSelectedRating(0);
      setShowReviewModal(false);
    } catch (error) {
      toast({
        title: t("testimonials.addReview.error.title", "Submission Failed"),
        description: t(
          "testimonials.addReview.error.submissionFailed",
          "There was an error submitting your review. Please try again.",
        ),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="bg-gradient-warm relative overflow-hidden py-24">
      {/* Background decorations */}
      <div className="absolute top-20 right-20 opacity-10">
        <Quote className="text-primary h-32 w-32" />
      </div>
      <div className="absolute bottom-20 left-20 opacity-10">
        <Heart className="text-primary h-24 w-24" />
      </div>

      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-20 text-center">
          <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
            <Heart className="text-primary mr-2 h-4 w-4" />
            <span className="text-primary text-sm font-semibold">
              Guest Stories
            </span>
          </div>
          <h2 className="text-foreground mb-6 text-3xl leading-tight font-bold md:text-4xl lg:text-5xl">
            {t("testimonials.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed">
            {t("testimonials.description")}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              className="group shadow-soft hover:shadow-elevated from-background to-brand-warm/30 cursor-pointer border-0 bg-gradient-to-br transition-all duration-500 hover:-translate-y-2"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <CardContent className="relative overflow-hidden p-8">
                {/* Quote decoration */}
                <div className="absolute top-4 right-4 opacity-10">
                  <Quote className="text-primary h-8 w-8" />
                </div>

                {/* Rating Stars */}
                <div className="mb-6 flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-5 w-5 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  {testimonial.verified && (
                    <Badge className="bg-green-100 text-green-700 hover:bg-green-200">
                      ✓ Verified
                    </Badge>
                  )}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-foreground relative mb-6 leading-relaxed">
                  <span className="text-primary absolute -top-2 -left-1 font-serif text-4xl opacity-20">
                    "
                  </span>
                  <span className="relative z-10">
                    {t(testimonial.textKey)}
                  </span>
                </blockquote>

                {/* Author */}
                <div
                  className={`flex items-center ${
                    isRTL ? "space-x-4 space-x-reverse" : "space-x-4"
                  }`}
                >
                  <Avatar className="ring-primary/20 h-14 w-14 ring-2">
                    <AvatarImage src="" alt={testimonial.name} />
                    <AvatarFallback className="bg-gradient-ocean text-lg font-bold text-white">
                      {testimonial.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-foreground group-hover:text-primary font-bold transition-colors duration-300">
                      {testimonial.name}
                    </p>
                    <p className="text-muted-foreground flex items-center text-sm">
                      <MapPin className="mr-1 h-3 w-3" />
                      {testimonial.location}
                    </p>
                  </div>
                </div>

                {/* Decorative line */}
                <div className="via-primary/30 absolute bottom-0 left-0 h-1 w-full scale-x-0 transform bg-gradient-to-r from-transparent to-transparent transition-transform duration-300 group-hover:scale-x-100"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Add Review Button */}
        <div className="mt-16 text-center">
          <button
            onClick={() => {
              setShowReviewModal(true);
              setSelectedRating(0);
            }}
            className="bg-gradient-ocean focus:ring-primary/20 transform rounded-xl px-8 py-4 font-semibold text-white transition-all duration-300 hover:-translate-y-1 hover:shadow-lg focus:ring-4"
          >
            {t("testimonials.addReview.button", "Share Your Experience")}
          </button>
        </div>

        {/* Review Modal */}
        {showReviewModal && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4 backdrop-blur-sm"
            onClick={() => {
              setShowReviewModal(false);
              setSelectedRating(0);
              setReviewText("");
            }}
          >
            <div
              className={`bg-background/95 shadow-soft border-primary/10 max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-2xl border p-8 backdrop-blur-sm ${
                isRTL ? "text-right" : "text-left"
              }`}
              onClick={(e) => e.stopPropagation()}
              dir={isRTL ? "rtl" : "ltr"}
            >
              <div
                className={`flex ${
                  isRTL ? "flex-row-reverse" : "flex-row"
                } mb-6 items-center justify-between`}
              >
                <h3 className="text-foreground text-2xl font-bold">
                  {t("testimonials.addReview.title", "Share Your Experience")}
                </h3>
                <button
                  onClick={() => {
                    setShowReviewModal(false);
                    setSelectedRating(0);
                    setReviewText("");
                  }}
                  className="hover:bg-muted rounded-lg p-2 transition-colors duration-200"
                >
                  <X className="text-muted-foreground h-6 w-6" />
                </button>
              </div>

              <p className="text-muted-foreground mb-6">
                {t(
                  "testimonials.addReview.description",
                  "Help other travelers by sharing your luxury accommodation experience",
                )}
              </p>

              <form className="space-y-6" onSubmit={handleSubmitReview}>
                {/* Rating */}
                <div className="text-center">
                  <label className="text-foreground mb-3 block text-sm font-medium">
                    {t("testimonials.addReview.rating", "Your Rating")}
                  </label>
                  <div
                    className={`flex items-center justify-center ${
                      isRTL ? "space-x-2 space-x-reverse" : "space-x-2"
                    }`}
                  >
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        className="text-2xl transition-transform duration-200 hover:scale-110"
                        onClick={() => setSelectedRating(star)}
                      >
                        <Star
                          className={`h-8 w-8 transition-colors duration-200 ${
                            star <= selectedRating
                              ? "fill-yellow-400 text-yellow-400"
                              : "fill-gray-300 text-gray-300 hover:fill-yellow-200 hover:text-yellow-200"
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                </div>

                {/* Review Text */}
                <div>
                  <label
                    htmlFor="review-text"
                    className={`text-foreground mb-2 block text-sm font-medium ${
                      isRTL ? "text-right" : "text-left"
                    }`}
                  >
                    {t("testimonials.addReview.review", "Your Review")}
                  </label>
                  <textarea
                    id="review-text"
                    rows={4}
                    value={reviewText}
                    onChange={(e) => setReviewText(e.target.value)}
                    className={`border-border bg-background/50 text-foreground placeholder-muted-foreground focus:ring-primary/20 focus:border-primary w-full resize-none rounded-lg border px-4 py-3 backdrop-blur-sm transition-all duration-200 focus:ring-2 ${
                      isRTL ? "text-right" : "text-left"
                    }`}
                    placeholder={t(
                      "testimonials.addReview.placeholder",
                      "Tell us about your experience...",
                    )}
                    dir={isRTL ? "rtl" : "ltr"}
                  />
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-gradient-ocean focus:ring-primary/20 w-full transform rounded-xl px-8 py-4 font-semibold text-white transition-all duration-300 hover:-translate-y-1 hover:shadow-lg focus:ring-4 disabled:transform-none disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isSubmitting
                      ? t("testimonials.addReview.submitting", "Submitting...")
                      : t("testimonials.addReview.submit", "Submit Review")}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default TestimonialsSection;
