import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "@/i18n/navigation";
import { ArrowR<PERSON>, Contact2Icon } from "lucide-react";
import { getTranslations } from "next-intl/server";

const CTASection: React.FC = async () => {
  const t = await getTranslations();

  return (
    <section className="from-primary/15 via-primary/5 to-primary/5 relative overflow-hidden bg-gradient-to-br py-24">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="p-12 text-center">
          <h2 className="mb-4 text-3xl font-bold md:text-4xl">
            {t("about.cta.title")}
          </h2>
          <p className="mx-auto mb-8 max-w-2xl text-lg">
            {t("about.cta.description")}
          </p>
          <div className="mx-auto grid max-w-sm grid-cols-1 gap-4 sm:grid-cols-2">
            <Button
              size="lg"
              variant="secondary"
              className="grow transition-transform bg-card text-primary border-primary duration-300 border font-semibold py-6 px-6 rounded-full hover:scale-105"
              asChild
            >
              <Link href="/contact">
                {t("about.cta.contact")}
                <Contact2Icon className="h-5 w-5" />
              </Link>
            </Button>
            <Button
              size="lg"
              className="grow transition-transform text-white border font-semibold duration-300 py-6 px-6 rounded-full hover:scale-105"
              asChild
            >
              <Link href="/real-estate">
                {t("about.cta.explore")}
                <ArrowRight className="h-5 w-5 rtl:rotate-180" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
