"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { FloatingInput } from "@/components/ui/floating-input";
import { useCreateInvestor } from "@/hooks/investor";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useMemo, useState } from "react";
import {
  ArrowRight,
  Building2,
  Mail,
  MessageSquare,
  Phone,
  Send,
  User,
} from "lucide-react";
import { createTranslator, useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import FormErrors from "@/components/FormErrors";
import FormFeedback from "@/components/FormFeedback";

function createAppointmentFormSchema(t: ReturnType<typeof createTranslator>) {
  return z.object({
    fullName: z
      .string()
      .min(1, t("validations.required"))
      .max(100, t("validations.maxLength", { max: 100 })),
    email: z
      .string()
      .min(1, t("validations.required"))
      .email(t("validations.email")),

    phone: z
      .string()
      .min(1, t("validations.required"))
      .regex(
        /^[0-9]{10,15}$/,
        t("validations.phoneDigitsRange", { min: 10, max: 15 }),
      ),
    propertyLocation: z
      .string()
      .min(2, t("validations.minLength", { min: 2 }))
      .max(200, t("validations.maxLength", { max: 200 })),
    message: z
      .string()
      .min(5, t("validations.minLength", { min: 5 }))
      .max(1000, t("validations.maxLength", { max: 1000 })),
  });
}

type AppointmentFormSchema = ReturnType<typeof createAppointmentFormSchema>;
type AppointmentFormValues = z.infer<AppointmentFormSchema>;

export function AppointmentForm({
  buttonClassName,
}: {
  buttonClassName?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const {
    mutate: createInvestor,
    isPending,
    error,
    isError,
    reset,
  } = useCreateInvestor();
  const t = useTranslations();
  const [isSuccess, setIsSuccess] = useState(false);

  const formSchema = useMemo(() => createAppointmentFormSchema(t), [t]);

  const form = useForm<AppointmentFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      propertyLocation: "",
      message: "",
    },
  });

  function onSubmit(values: AppointmentFormValues) {
    createInvestor(values, {
      onSuccess: () => {
        form.reset();
        setIsSuccess(true);

        setIsOpen(false);
      },
    });
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          form.reset();
          reset();
          setIsSuccess(false);
        }
        setIsOpen(open);
      }}
    >
      <DialogTrigger asChild>
        <Button
          size="lg"
          className={cn(
            "group rounded-full bg-white !px-6 py-7 text-lg font-semibold text-[#279fc7] shadow-lg transition-all duration-300 hover:scale-105 hover:bg-gray-100 hover:shadow-xl",
            buttonClassName,
          )}
        >
          {t("realEstate.hero.cta")}
          <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1 rtl:rotate-180 rtl:group-hover:-translate-x-1" />
        </Button>
      </DialogTrigger>
      <DialogContent showCloseButton={false} className="p-6 sm:max-w-[600px]">
        <DialogHeader>
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-bold">
              {t("contact.form.title")}
            </DialogTitle>
            <DialogDescription className="mb-4 text-center">
              {t("contact.form.description")}
            </DialogDescription>
          </DialogHeader>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        label={t("contact.form.name")}
                        icon={User}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="propertyLocation"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        label={t("contact.form.location")}
                        icon={Building2}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        type="tel"
                        label={t("contact.form.email")}
                        icon={Mail}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <FloatingInput
                        type="tel"
                        label={t("contact.form.phone")}
                        icon={Phone}
                        error={fieldState.error?.message}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="message"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <FloatingInput
                      className="h-18"
                      type="textarea"
                      label={t("contact.form.message")}
                      icon={MessageSquare}
                      error={fieldState.error?.message}
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {isError && <FormErrors error={error} />}

            <Button className="w-full p-6" type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                  {t("contact.form.sending")}
                </>
              ) : (
                <>
                  <Send className="mr-2 h-5 w-5" />
                  {t("contact.form.submit")}
                </>
              )}
            </Button>
          </form>
        </Form>
      </DialogContent>

      <FormFeedback open={isSuccess} onOpenChange={setIsSuccess} />
    </Dialog>
  );
}
