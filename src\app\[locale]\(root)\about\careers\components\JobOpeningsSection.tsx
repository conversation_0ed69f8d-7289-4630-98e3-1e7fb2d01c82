import { useTranslations } from "next-intl";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Briefcase, MapPin, Clock, Star, Send } from "lucide-react";

interface JobOpening {
  key: string;
  title: string;
  department: string;
  location: string;
  type: string;
  experience: string;
}

const JobOpeningsSection: React.FC = () => {
  const t = useTranslations();

  const jobOpenings: JobOpening[] = [
    {
      key: "frontend-developer",
      title: t("careers.jobs.items.frontend-developer.title"),
      department: t("careers.jobs.items.frontend-developer.department"),
      location: t("careers.jobs.items.frontend-developer.location"),
      type: t("careers.jobs.items.frontend-developer.type"),
      experience: t("careers.jobs.items.frontend-developer.experience"),
    },
    {
      key: "backend-developer",
      title: t("careers.jobs.items.backend-developer.title"),
      department: t("careers.jobs.items.backend-developer.department"),
      location: t("careers.jobs.items.backend-developer.location"),
      type: t("careers.jobs.items.backend-developer.type"),
      experience: t("careers.jobs.items.backend-developer.experience"),
    },
    {
      key: "product-manager",
      title: t("careers.jobs.items.product-manager.title"),
      department: t("careers.jobs.items.product-manager.department"),
      location: t("careers.jobs.items.product-manager.location"),
      type: t("careers.jobs.items.product-manager.type"),
      experience: t("careers.jobs.items.product-manager.experience"),
    },
  ];

  return (
    <section className="bg-background py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <Badge className="bg-brand-teal-dark/10 text-brand-teal-dark border-brand-teal-dark/20 mb-4">
            {t("careers.jobs.badge")}
          </Badge>
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-5xl">
            {t("careers.jobs.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
            {t("careers.jobs.subtitle")}
          </p>
        </div>

        <div className="grid gap-6">
          {jobOpenings.map((job, index) => (
            <Card
              key={job.key}
              className="shadow-soft bg-background hover:shadow-elevated group overflow-hidden border-0 transition-all duration-500 hover:scale-[1.02]"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="from-primary/5 to-brand-ocean/5 absolute inset-0 bg-gradient-to-r opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>
              <CardContent className="relative z-10 p-8">
                <div className="flex flex-col justify-between gap-6 lg:flex-row lg:items-center">
                  <div className="flex-1">
                    <div className="mb-4 flex items-center gap-3">
                      <div className="from-primary to-brand-ocean flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-r">
                        <Briefcase className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-foreground text-2xl font-bold">
                          {job.title}
                        </h3>
                        <p className="text-brand-ocean font-medium">
                          {job.department}
                        </p>
                      </div>
                    </div>
                    <div className="text-muted-foreground flex flex-wrap gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {job.location}
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        {job.type}
                      </div>
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4" />
                        {job.experience}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col gap-3 sm:flex-row">
                    <Button
                      variant="outline"
                      className="border-primary text-primary hover:bg-primary border-2 transition-all duration-300 hover:text-white"
                    >
                      {t("careers.jobs.viewDetails")}
                    </Button>
                    <Button className="bg-primary hover:bg-primary/90 text-white transition-all duration-300">
                      {t("careers.jobs.apply")}
                      <Send className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default JobOpeningsSection;
