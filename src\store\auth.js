import { clearTokenRefresh, scheduleTokenRefresh } from "@/lib/authManager";
import api from "@/lib/axios";
import { jwtDecode } from "jwt-decode";
import { create } from "zustand";

const handleAuthSuccess = (data, set) => {
  localStorage.setItem("token", data.token);
  localStorage.setItem("refreshToken", data.refreshToken);
  scheduleTokenRefresh(data.expiresIn);
  const decoded = jwtDecode(data.token);

  set({
    role: decoded.roles?.[0] || null,
    user: data,
    token: data.token,
    loading: false,
    error: null,
  });
};

export const useAuthStore = create((set) => ({
  user: null,
  token: null,
  role: null,
  loading: false,
  error: null,

  // ✅ دي عشان نقدر نناديها من axios interceptor
  setAuthData: ({ token, role }) =>
    set({
      token,
      role,
    }),

  login: async (email, password) => {
    set({ loading: true, error: null });
    try {
      const response = await api.post("/Auth", { email, password });
      const data = response.data;

      if (data.token) {
        handleAuthSuccess(data, set);
        return { success: true, data, statusCode: response.status };
      } else {
        throw new Error("No token received");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Login failed";
      set({ loading: false, error: errorMessage });
      return {
        success: false,
        error: errorMessage,
        statusCode: err.response?.status,
      };
    }
  },

  register: async (firstName, lastName, phoneNumber, email, password) => {
    set({ loading: true, error: null });
    try {
      // Create FormData for multipart/form-data
      const formData = new FormData();
      formData.append("FirstName", firstName);
      formData.append("LastName", lastName);
      formData.append("PhoneNumber", phoneNumber);
      formData.append("Email", email);
      formData.append("Password", password);

      const response = await api.post("/Auth/register", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const data = response.data;

      // Check if the response contains the success message
      if (
        data &&
        typeof data === "string" &&
        data.includes("Registration completed successfully")
      ) {
        set({ loading: false, error: null });
        return { success: true, message: data, statusCode: response.status };
      } else if (data.token) {
        // If token is received, handle as successful login
        handleAuthSuccess(data, set);
        return { success: true, data, statusCode: response.status };
      } else {
        throw new Error("Registration failed");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Registration failed";
      set({ loading: false, error: errorMessage });
      return {
        success: false,
        error: errorMessage,
        statusCode: err.response?.status,
      };
    }
  },

  signIn: async () => {
    set({ loading: true, error: null });
    try {
      const response = await api.get("/Auth/signin");
      const data = response.data;
      if (data.token) {
        handleAuthSuccess(data, set);
        return { success: true, data };
      } else {
        throw new Error("No token received");
      }
    } catch (err) {
      const errorMessage =
        err.response?.data?.message || err.message || "Sign in failed";
      set({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  },

  logout: () => {
    localStorage.removeItem("token");
    localStorage.removeItem("refreshToken");
    clearTokenRefresh();
    set({ user: null, token: null, role: null, error: null });
  },

  clearError: () => {
    set({ error: null });
  },

  updateUser: (updatedUser) => {
    set({ user: updatedUser });
  },

  isAdmin: () => {
    const token = localStorage.getItem("token");

    if (!token) {
      return false;
    }

    try {
      const decoded = jwtDecode(token);
      return decoded.roles?.includes("Admin");
    } catch (error) {
      console.error("Error decoding token:", error);
      return false;
    }
  },
}));
