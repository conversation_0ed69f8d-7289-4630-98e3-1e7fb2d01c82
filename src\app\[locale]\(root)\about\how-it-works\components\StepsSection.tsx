import { Card, CardContent } from "@/components/ui/card";
import { Calendar, CheckCircle, Search, Star } from "lucide-react";
import { getLocale, getMessages } from "next-intl/server";

const StepsSection: React.FC = async () => {
  const locale = await getLocale();
  const messages = await getMessages({ locale });
  const stepsMessages = messages.howItWorks.steps;

  const steps = [
    {
      number: "01",
      icon: Search,
      data: stepsMessages.search,
      color: "from-blue-500 to-cyan-500",
    },
    {
      number: "02",
      icon: Calendar,
      data: stepsMessages.book,
      color: "from-cyan-500 to-teal-500",
    },
    {
      number: "03",
      icon: Star,
      data: stepsMessages.experience,
      color: "from-teal-500 to-emerald-500",
    },
  ];

  return (
    <section className="bg-primary/3 py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid gap-12 md:grid-cols-2 md:gap-6 lg:grid-cols-3 lg:gap-8">
          {steps.map((step) => (
            <div key={step.data.title} className="group relative">
              <Card className="shadow-soft bg-background hover:shadow-elevated border-primary/50 relative overflow-hidden transition-all duration-500 group-hover:scale-105">
                <div className="from-primary/5 to-brand-ocean/5 absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-500 group-hover:opacity-100" />

                <div className="from-primary/10 to-brand-ocean/10 absolute start-3 top-3 inline-flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br transition-transform duration-300 group-hover:scale-110">
                  <span className="from-primary to-brand-ocean bg-gradient-to-r bg-clip-text text-2xl font-bold text-transparent">
                    {step.number}
                  </span>
                </div>

                <CardContent className="relative z-10 p-8 text-center">
                  <div
                    className={`inline-flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br ${step.color} shadow-soft mt-4 mb-6 transition-transform duration-300 group-hover:scale-110`}
                  >
                    <step.icon className="h-10 w-10 text-white" />
                  </div>

                  <h3 className="text-foreground mb-4 text-2xl font-bold">
                    {step.data.title}
                  </h3>
                  <p className="text-muted-foreground mb-6 max-w-lg leading-relaxed">
                    {step.data.description}
                  </p>

                  <ul className="space-y-3">
                    {Object.values(step.data.features).map((feature) => (
                      <li
                        key={feature}
                        className="text-muted-foreground flex items-center text-sm md:text-base"
                      >
                        <CheckCircle className="text-primary me-2 size-4 flex-shrink-0 md:size-5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StepsSection;
