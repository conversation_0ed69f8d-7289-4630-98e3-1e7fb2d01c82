import { HelpCircle, MessageCircle } from "lucide-react";
import { useTranslations } from "next-intl";

const HeroSection: React.FC = () => {
  const t = useTranslations();

  return (
    <section className="bg-background relative overflow-hidden pt-24">
      {/* Background decorations */}
      <div className="absolute top-20 right-10 opacity-5">
        <HelpCircle className="text-primary h-32 w-32" />
      </div>
      <div className="absolute bottom-20 left-10 opacity-5">
        <MessageCircle className="text-primary h-24 w-24" />
      </div>

      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
            <HelpCircle className="text-primary mr-2 h-4 w-4" />
            <span className="text-primary text-sm font-semibold">
              {t("faq.subtitle")}
            </span>
          </div>
          <h1 className="text-foreground mb-6 text-3xl leading-tight font-bold md:text-4xl lg:text-5xl">
            {t("faq.title")}
          </h1>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed">
            {t("faq.description")}
          </p>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
