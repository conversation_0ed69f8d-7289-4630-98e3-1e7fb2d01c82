import { CorporateRequestStatus } from "../../types/corporateRequests";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Check, Pencil } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { statusClasses, statusLabels } from "./CorporateFilterByStatus";
import { cn } from "@/lib/utils";
import useUpdateCorporateRequestStatus from "../../hooks/corporateRequests/useUpdateCorporateRequestStatus";

export default function UpdateCorporateRequestStatusButton({
  currentStatus,
  id,
}: {
  currentStatus: CorporateRequestStatus;
  id: string;
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [status, setStatus] = useState<CorporateRequestStatus>(currentStatus);

  const updateCorporateRequest = useUpdateCorporateRequestStatus();

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label="تغيير الحالة"
            variant="default"
            size="icon"
            className="rounded-full"
            onClick={() => setIsDialogOpen(true)}
          >
            <Pencil className="size-4" />
            <span className="sr-only">تغيير الحالة</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent variant="primary">تغيير الحالة</TooltipContent>
      </Tooltip>

      {isDialogOpen && (
        <Dialog
          open
          onOpenChange={(open) => {
            setStatus(currentStatus);
            setIsDialogOpen(open);
          }}
        >
          <DialogContent>
            <DialogHeader className="items-start">
              <DialogTitle>تغيير الحالة</DialogTitle>
              <DialogDescription>قم بتعديل حالة الطلب</DialogDescription>
            </DialogHeader>

            <div className="flex flex-wrap gap-2">
              {Object.entries(statusLabels).map(([key, value]) => (
                <button
                  type="button"
                  key={key}
                  onClick={() =>
                    setStatus(key as unknown as CorporateRequestStatus)
                  }
                  className={cn(
                    "flex items-center gap-2 rounded-full px-4.5 py-1.5 text-sm font-medium sm:text-base",
                    [statusClasses[key as unknown as CorporateRequestStatus]],
                  )}
                >
                  {value}
                  {status == (key as unknown as CorporateRequestStatus) && (
                    <Check className="size-4" />
                  )}
                </button>
              ))}
            </div>

            <DialogFooter className="justify-start">
              <Button
                disabled={updateCorporateRequest.isPending}
                variant="secondary"
                onClick={() => {
                  setIsDialogOpen(false);
                  setStatus(currentStatus);
                }}
              >
                إلغاء
              </Button>
              <Button
                disabled={
                  updateCorporateRequest.isPending || currentStatus === status
                }
                onClick={() =>
                  updateCorporateRequest.mutate(
                    { id, status },
                    {
                      onSuccess: () => {
                        setIsDialogOpen(false);
                      },
                    },
                  )
                }
              >
                {updateCorporateRequest.isPending ? "جاري التحديث..." : "تأكيد"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
