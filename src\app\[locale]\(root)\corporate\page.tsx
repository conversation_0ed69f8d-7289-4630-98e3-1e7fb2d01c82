import ScrollRevealSection from "@/components/ScrollReveal";
import {
  AccommodationTypes,
  AmenitiesSection,
  BenefitsSection,
  CTASection,
  HeroSection,
  TrustedBySection,
  ValueProposition,
} from "./components";

const CorporateStay: React.FC = () => {
  return (
    <div className="bg-background text-foreground min-h-screen">
      <HeroSection />

      <ScrollRevealSection>
        <ValueProposition />
      </ScrollRevealSection>

      <ScrollRevealSection>
        <AccommodationTypes />
      </ScrollRevealSection>

      <ScrollRevealSection>
        <BenefitsSection />
      </ScrollRevealSection>

      <ScrollRevealSection>
        <AmenitiesSection />
      </ScrollRevealSection>

      <TrustedBySection />

      <ScrollRevealSection>
        <CTASection />
      </ScrollRevealSection>
    </div>
  );
};

export default CorporateStay;
