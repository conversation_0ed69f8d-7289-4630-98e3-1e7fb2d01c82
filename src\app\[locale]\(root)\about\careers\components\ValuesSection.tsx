import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { Heart, Users, Lightbulb, Target } from "lucide-react";

interface Value {
  icon: React.ElementType;
  key: string;
  color: string;
}

const ValuesSection: React.FC = () => {
  const t = useTranslations("careers.values");

  const values: Value[] = [
    {
      icon: Heart,
      key: "passion",
      color: "from-red-500 to-pink-500",
    },
    {
      icon: Users,
      key: "teamwork",
      color: "from-blue-500 to-cyan-500",
    },
    {
      icon: Lightbulb,
      key: "innovation",
      color: "from-yellow-500 to-orange-500",
    },
    {
      icon: Target,
      key: "excellence",
      color: "from-green-500 to-emerald-500",
    },
  ];

  return (
    <section className="bg-background py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <Badge className="bg-brand-ocean/10 text-brand-ocean border-brand-ocean/20 mb-4">
            {t("badge")}
          </Badge>
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-5xl">
            {t("title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
            {t("subtitle")}
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {values.map((value) => (
            <div
              key={value.key}
              className="group bg-card border-border relative overflow-hidden rounded-2xl border p-8 shadow-sm transition-all duration-300 hover:shadow-xl"
            >
              <div
                className={`absolute top-0 left-0 h-1 w-full bg-gradient-to-r ${value.color} opacity-50 transition-opacity duration-300 group-hover:opacity-100`}
              ></div>
              <div className="relative z-10">
                <div
                  className={`mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br ${value.color} text-white shadow-lg transition-transform duration-300 group-hover:scale-110`}
                >
                  <value.icon className="h-8 w-8" />
                </div>
                <h3 className="text-foreground mb-3 text-2xl font-bold">
                  {t(`items.${value.key}.title`)}
                </h3>
                <p className="text-muted-foreground">
                  {t(`items.${value.key}.description`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ValuesSection;
