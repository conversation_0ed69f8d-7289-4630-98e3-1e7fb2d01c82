import { Card } from "@/components/ui/card";
import { MapPin, Shield, Smartphone, TimerResetIcon } from "lucide-react";
import { getLocale, getMessages } from "next-intl/server";

const BenefitsSection: React.FC = async () => {
  const locale = await getLocale();
  const messages = await getMessages({ locale });
  const benefitsMessages = messages.howItWorks.benefits;

  const benefits = [
    {
      icon: Shield,
      title: benefitsMessages.items.verified.title,
      description: benefitsMessages.items.verified.description,
    },
    {
      icon: TimerResetIcon,
      title: benefitsMessages.items.support.title,
      description: benefitsMessages.items.support.description,
    },
    {
      icon: Smartphone,
      title: benefitsMessages.items.technology.title,
      description: benefitsMessages.items.technology.description,
    },
    {
      icon: MapPin,
      title: benefitsMessages.items.locations.title,
      description: benefitsMessages.items.locations.description,
    },
  ];

  return (
    <section className="dark:bg-secondary/30 bg-secondary py-20 lg:py-32">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
            {benefitsMessages.title}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-xl">
            {benefitsMessages.subtitle}
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {benefits.map((benefit) => (
            <Card
              key={benefit.title}
              className="shadow-soft bg-background hover:shadow-elevated group border-primary/30 border-2 transition-all duration-300 hover:scale-105"
            >
              <div className="p-8 text-center">
                <div
                  className={`bg-primary/10 text-primary shadow-soft mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl transition-transform duration-300 group-hover:scale-110`}
                >
                  <benefit.icon className="text-primary h-8 w-8" />
                </div>
                <h3 className="text-foreground mb-2 text-xl font-bold">
                  {benefit.title}
                </h3>
                <p className="text-muted-foreground">{benefit.description}</p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
