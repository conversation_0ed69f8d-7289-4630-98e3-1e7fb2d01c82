import { Card, CardContent } from "@/components/ui/card";
import {
  Wifi,
  Car,
  Snowflake,
  WashingMachine,
  Users,
  Monitor,
  Sparkles,
  Building2,
  CheckCircle,
  LucideProps,
} from "lucide-react";
import { getTranslations } from "next-intl/server";

interface Amenity {
  icon: React.ComponentType<LucideProps>;
  title: string;
}

const AmenitiesSection: React.FC = async () => {
  const t = await getTranslations();

  const amenities: Amenity[] = [
    { icon: Wifi, title: t("corporate.amenities.wifi") },
    { icon: Snowflake, title: t("corporate.amenities.airConditioning") },
    { icon: Car, title: t("corporate.amenities.parking") },
    {
      icon: WashingMachine,
      title: t("corporate.amenities.washingMachine"),
    },
    {
      icon: Users,
      title: t("corporate.amenities.collaborativeWorkspace"),
    },
    { icon: Monitor, title: t("corporate.amenities.workstation") },
    { icon: Spark<PERSON>, title: t("corporate.amenities.cleaning") },
    { icon: Building2, title: t("corporate.amenities.fullyFurnished") },
    {
      icon: CheckCircle,
      title: t("corporate.amenities.accessibilityFeatures"),
    },
  ];

  return (
    <section className="bg-background py-16">
      <div className="container mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="text-foreground mb-4 text-3xl font-bold md:text-4xl">
            {t("corporate.accommodation.amenities.title")}
          </h2>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {amenities.map((amenity, index) => {
            const IconComponent = amenity.icon;
            return (
              <Card
                key={index}
                className="bg-card text-card-foreground group transition-all duration-300 hover:scale-105 hover:shadow-lg"
              >
                <CardContent className="p-6 text-center">
                  <div className="mb-4 flex justify-center">
                    <div className="bg-primary/10 group-hover:bg-primary/20 rounded-full p-3 transition-colors duration-300">
                      <IconComponent className="text-primary h-8 w-8" />
                    </div>
                  </div>
                  <h3 className="text-foreground mb-2 text-lg font-semibold md:text-xl">
                    {amenity.title}
                  </h3>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default AmenitiesSection;
