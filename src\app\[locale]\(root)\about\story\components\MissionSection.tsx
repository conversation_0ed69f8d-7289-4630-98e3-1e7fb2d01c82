import { Card, CardContent } from "@/components/ui/card";
import { Award, CheckCir<PERSON>, Heart } from "lucide-react";
import { getTranslations } from "next-intl/server";

const MissionSection: React.FC = async () => {
  const t = await getTranslations();

  return (
    <section className="bg-background py-20">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          <div>
            <div className="bg-primary/10 mb-6 inline-flex items-center rounded-full px-4 py-2">
              <Heart className="text-primary me-2 size-4 stroke-3 md:size-5" />
              <span className="text-primary text-sm font-semibold md:text-base lg:text-lg">
                {t("about.mission.subtitle")}
              </span>
            </div>
            <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
              {t("about.mission.title")}
            </h2>
            <p className="text-muted-foreground mb-8 text-lg leading-relaxed font-medium md:text-xl">
              {t("about.mission.description")}
            </p>
            <div className="space-y-4">
              {[
                t("about.mission.points.luxury"),
                t("about.mission.points.technology"),
                t("about.mission.points.service"),
              ].map((point, index) => (
                <div key={index} className="flex items-start">
                  <CheckCircle className="text-primary me-3 size-5 flex-shrink-0 stroke-3" />
                  <span className="text-muted-foreground font-semibold">
                    {point}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <Card className="shadow-elevated from-primary/5 to-brand-ocean/10 border-0 bg-gradient-to-br">
              <CardContent className="px-8 py-6">
                <div className="bg-primary/30 mb-6 grid aspect-square size-16 w-fit place-content-center rounded-full p-2">
                  <Award className="text-primary h-12 w-12" />
                </div>

                <h3 className="text-foreground mb-4 text-xl font-bold md:text-2xl">
                  {t("about.mission.award.title")}
                </h3>

                <p className="text-muted-foreground leading-relaxed font-semibold md:text-lg">
                  {t("about.mission.award.description")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MissionSection;
