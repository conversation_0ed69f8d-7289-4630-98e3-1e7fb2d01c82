import { Card } from "@/components/ui/card";
import { Users, Building, Handshake } from "lucide-react";
import { getTranslations } from "next-intl/server";

interface PartnerType {
  icon: React.ElementType;
  title: string;
  description: string;
}

const PartnerTypesSection: React.FC = async () => {
  const t = await getTranslations("realEstate.partnerTypes");

  const partnerTypes: PartnerType[] = [
    {
      icon: Users,
      title: t("propertyOwners.title"),
      description: t("propertyOwners.description"),
    },
    {
      icon: Building,
      title: t("developers.title"),
      description: t("developers.description"),
    },
    {
      icon: Handshake,
      title: t("agents.title"),
      description: t("agents.description"),
    },
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold md:text-4xl">{t("title")}</h2>
          <p className="text-muted-foreground mt-2 text-lg">
            {t("description")}
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {partnerTypes.map((partner, index) => (
            <Card
              key={index}
              className="bg-card text-card-foreground transform border p-10 text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl"
            >
              <div className="bg-secondary border-muted-foreground/20 mx-auto flex h-16 w-16 items-center justify-center rounded-full border-2">
                <partner.icon className="text-primary h-8 w-8" />
              </div>
              <h3 className="text-primary mb-2 text-xl font-semibold">
                {partner.title}
              </h3>
              <p className="text-secondary-foreground/80">
                {partner.description}
              </p>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PartnerTypesSection;
