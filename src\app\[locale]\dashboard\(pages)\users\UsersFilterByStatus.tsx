"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { UserStatus } from "../../types/users";

export const UserStatusArabic: Record<UserStatus, string> = {
  [UserStatus.NotApproved]: "غير معتمد",
  [UserStatus.Pending]: "معلق",
  [UserStatus.Approved]: "معتمد",
  [UserStatus.Rejected]: "مرفوض",
};

export default function UsersFilterByStatus() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const statusParam = searchParams.get("status");
  const statusValue = statusParam !== null ? statusParam : "all";

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm">الحالة:</span>
      <Select
        dir="rtl"
        value={statusValue}
        onValueChange={(v) => {
          const params = new URLSearchParams(searchParams.toString());

          if (v === "all") {
            params.delete("status");
          } else {
            params.set("status", v);
          }

          // reset to first page when filter changes
          params.set("pageNumber", "1");

          router.push(`${pathname}?${params.toString()}`, {
            scroll: false,
          });
        }}
      >
        <SelectTrigger>
          <SelectValue placeholder="اختر الحالة" />
        </SelectTrigger>
        <SelectContent align="start">
          <SelectItem value="all">الكل</SelectItem>
          <SelectItem value={String(UserStatus.NotApproved)}>
            {UserStatusArabic[UserStatus.NotApproved]}
          </SelectItem>
          <SelectItem value={String(UserStatus.Pending)}>
            {UserStatusArabic[UserStatus.Pending]}
          </SelectItem>
          <SelectItem value={String(UserStatus.Approved)}>
            {UserStatusArabic[UserStatus.Approved]}
          </SelectItem>
          <SelectItem value={String(UserStatus.Rejected)}>
            {UserStatusArabic[UserStatus.Rejected]}
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
