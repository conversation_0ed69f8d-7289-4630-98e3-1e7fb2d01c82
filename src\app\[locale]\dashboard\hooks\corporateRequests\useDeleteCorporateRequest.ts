import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { deleteCorporateRequest } from "../../services/corporateRequests";

export default function useDeleteCorporateRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteCorporateRequest(id),
    onSuccess: () => {
      toast.success("تم حذف طلبك بنجاح.");
      queryClient.invalidateQueries({
        queryKey: ["corporate-requests"],
      });
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });
}
