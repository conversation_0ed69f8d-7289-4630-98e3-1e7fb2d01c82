"use client";

import { useTranslationWithLanguage } from "@/hooks/useTranslationWithLanguage";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowR<PERSON>, Sparkles, Star, Users, MapPin } from "lucide-react";

const CTASection = () => {
  const { t, isRTL } = useTranslationWithLanguage();
  const router = useRouter();

  const navigate = (route: string) => {
    router.push(route);
  };

  return (
    <section className="from-primary/10 via-background to-brand-ocean/10 relative overflow-hidden bg-gradient-to-br py-24">
      {/* Background decorations */}
      <div className="via-primary/5 absolute inset-0 bg-gradient-to-r from-transparent to-transparent"></div>
      <div className="absolute top-20 left-20 opacity-10">
        <Sparkles className="text-primary h-32 w-32 animate-pulse" />
      </div>
      <div className="absolute right-20 bottom-20 opacity-10">
        <Star
          className="text-primary h-24 w-24 animate-pulse"
          style={{ animationDelay: "1s" }}
        />
      </div>

      <div className="relative mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <Card className="shadow-elevated from-background via-background to-brand-warm/20 overflow-hidden border-0 bg-gradient-to-br">
          <CardContent className="relative p-12 text-center md:p-16">
            {/* Decorative elements */}
            <div className="from-primary via-brand-ocean to-primary absolute top-0 left-0 h-2 w-full bg-gradient-to-r"></div>
            <div className="absolute top-8 right-8 opacity-20">
              <MapPin className="text-primary h-16 w-16" />
            </div>
            <div className="absolute bottom-8 left-8 opacity-20">
              <Users className="text-primary h-12 w-12" />
            </div>

            {/* Content */}
            <div className="mx-auto max-w-4xl">
              <div className="bg-primary/10 mb-8 inline-flex items-center rounded-full px-6 py-3">
                <Sparkles
                  className={`text-primary h-5 w-5 ${isRTL ? "ml-2" : "mr-2"}`}
                />
                <span className="text-primary font-semibold">
                  {t("cta.badge")}
                </span>
              </div>

              <h2 className="text-foreground mb-6 text-4xl leading-tight font-bold md:text-5xl lg:text-6xl">
                {t("cta.title")}
                <br />
                <span className="from-primary via-brand-ocean to-primary bg-gradient-to-r bg-clip-text text-transparent">
                  {t("cta.subtitle")}
                </span>
              </h2>

              <p className="text-muted-foreground mx-auto mb-12 max-w-3xl text-xl leading-relaxed">
                {t("cta.description")}
              </p>

              {/* Stats */}
              <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-3">
                <div className="text-center">
                  <div className="text-primary mb-2 text-3xl font-bold md:text-4xl">
                    50K+
                  </div>
                  <div className="text-muted-foreground text-sm">
                    {t("cta.stats.happyGuests")}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-primary mb-2 text-3xl font-bold md:text-4xl">
                    200+
                  </div>
                  <div className="text-muted-foreground text-sm">
                    {t("cta.stats.premiumProperties")}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-primary mb-2 text-3xl font-bold md:text-4xl">
                    4.9★
                  </div>
                  <div className="text-muted-foreground text-sm">
                    {t("cta.stats.averageRating")}
                  </div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
                <Button
                  size="lg"
                  className="bg-gradient-ocean shadow-soft hover:shadow-elevated px-8 py-6 text-lg font-semibold text-white transition-all duration-300 hover:scale-105 hover:opacity-90"
                  onClick={() => navigate("/units")}
                >
                  <Sparkles className={`h-5 w-5 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {t("cta.buttons.exploreProperties")}
                  <ArrowRight
                    className={`h-5 w-5 ${isRTL ? "mr-2 rotate-180" : "ml-2"}`}
                  />
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="border-primary text-primary hover:bg-primary border-2 px-8 py-6 text-lg font-semibold transition-all duration-300 hover:text-white"
                >
                  {t("cta.buttons.learnMore")}
                </Button>
              </div>

              {/* Trust indicators */}
              <div className="border-border/50 mt-12 border-t pt-8">
                <div className="text-muted-foreground flex flex-wrap items-center justify-center gap-8 text-sm">
                  <div
                    className={`flex items-center ${
                      isRTL ? "space-x-2 space-x-reverse" : "space-x-2"
                    }`}
                  >
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span>{t("cta.trustIndicators.trustedWorldwide")}</span>
                  </div>
                  <div
                    className={`flex items-center ${
                      isRTL ? "space-x-2 space-x-reverse" : "space-x-2"
                    }`}
                  >
                    <Users className="h-4 w-4" />
                    <span>{t("cta.trustIndicators.customerSupport")}</span>
                  </div>
                  <div
                    className={`flex items-center ${
                      isRTL ? "space-x-2 space-x-reverse" : "space-x-2"
                    }`}
                  >
                    <MapPin className="h-4 w-4" />
                    <span>{t("cta.trustIndicators.globalDestinations")}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default CTASection;
