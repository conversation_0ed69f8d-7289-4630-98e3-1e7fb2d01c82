import ScrollRevealSection from "@/components/ScrollReveal";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Building2,
  Users,
  Globe,
  Star,
  Sparkles,
  Shield,
  BookIcon,
} from "lucide-react";
import { getTranslations } from "next-intl/server";

interface Stat {
  number: string;
  label: string;
  icon: React.ElementType;
}

const HeroSection: React.FC = async () => {
  const t = await getTranslations();

  const stats: Stat[] = [
    {
      number: "50K+",
      label: t("about.stats.happyGuests"),
      icon: Users,
    },
    {
      number: "100+",
      label: t("about.stats.cities"),
      icon: Globe,
    },
    {
      number: "4.9",
      label: t("about.stats.rating"),
      icon: Star,
    },
    {
      number: "24/7",
      label: t("about.stats.support"),
      icon: Shield,
    },
  ];

  return (
    <section className="from-background via-brand-teal-dark/20 to-brand-teal-dark/10 relative overflow-hidden bg-gradient-to-br py-24">
      {/* Background decorations */}
      <div className="absolute top-20 right-10 opacity-5">
        <Building2 className="text-primary h-32 w-32" />
      </div>
      <div className="absolute bottom-20 left-10 opacity-5">
        <Sparkles className="text-primary h-24 w-24" />
      </div>

      <ScrollRevealSection>
        <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
          <div className="mb-16 text-center">
            <Badge className="border-border bg-card/40 mb-6 rounded-full px-6 py-2 font-semibold text-white transition-all duration-300">
              <BookIcon className="text-primary me-2 !size-4 md:!size-5" />
              <span className="text-primary text-sm font-semibold md:text-base">
                {t("about.subtitle")}
              </span>
            </Badge>

            <h1 className="text-foreground mb-6 text-4xl leading-tight font-bold md:text-5xl lg:text-6xl">
              {t("about.title")}
            </h1>

            <p className="text-muted-foreground mx-auto max-w-3xl text-xl leading-relaxed">
              {t("about.description")}
            </p>
          </div>

          {/* Stats Grid */}
          <div className="mb-16 grid grid-cols-2 gap-6 md:grid-cols-4">
            {stats.map((stat, index) => (
              <Card
                key={index}
                className="shadow-soft from-card/50 via-brand-teal-dark/10 to-brand-teal-dark/5 hover:shadow-elevated border-border/50 border-2 bg-gradient-to-br transition-all duration-300"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardContent className="p-4 text-center">
                  <div className="bg-primary/30 mx-auto mb-4 grid aspect-square size-14 w-fit place-content-center rounded-full p-2">
                    <stat.icon className="text-primary h-8 w-8" />
                  </div>
                  <div className="text-foreground mb-1 text-2xl font-bold md:text-3xl">
                    {stat.number}
                  </div>
                  <div className="text-muted-foreground text-sm font-semibold md:text-base">
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </ScrollRevealSection>
    </section>
  );
};

export default HeroSection;
