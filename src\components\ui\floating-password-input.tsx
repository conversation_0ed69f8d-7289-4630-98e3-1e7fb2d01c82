"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { LucideIcon, Eye, EyeOff } from "lucide-react";
import { useLocale } from "next-intl";
import { Button } from "@/components/ui/button";

export interface FloatingPasswordInputProps extends React.ComponentProps<"input"> {
  label: string;
  icon?: LucideIcon;
  error?: string;
}

const FloatingPasswordInput = React.forwardRef<HTMLInputElement, FloatingPasswordInputProps>(
  ({ className, label, icon: Icon, error, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(false);
    const [showPassword, setShowPassword] = React.useState(false);
    const locale = useLocale();

    const handleFocus = () => setIsFocused(true);
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      setHasValue(e.target.value.length > 0);
      props.onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(e.target.value.length > 0);
      props.onChange?.(e);
    };

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    const isLabelFloating = isFocused || hasValue;
    const isRTL = locale === "ar";

    return (
      <div className="w-full">
        <div className="relative">
          {/* Icon */}
          {Icon && (
            <Icon
              className={cn(
                "absolute top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground transition-colors duration-200",
                isRTL ? "right-3" : "left-3",
                isFocused && "text-primary",
                error && "text-destructive",
              )}
            />
          )}

          {/* Input */}
          <input
            type={showPassword ? "text" : "password"}
            ref={ref}
            className={cn(
              "peer border-input w-full rounded-md border bg-transparent px-3 py-3 text-base placeholder-transparent transition-all duration-200 ease-in-out outline-none",
              "focus:border-primary focus:ring-primary/20 focus:ring-2",
              Icon && (isRTL ? "pr-10" : "pl-10"),
              "pr-10", // Always add right padding for the toggle button
              error &&
                "border-destructive focus:border-destructive focus:ring-destructive/20",
              className,
            )}
            placeholder={label}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />

          {/* Password Toggle Button */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className={cn(
              "absolute top-1/2 h-8 w-8 -translate-y-1/2 p-0 hover:bg-transparent",
              isRTL ? "left-1" : "right-1",
            )}
            onClick={togglePasswordVisibility}
            tabIndex={-1}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-muted-foreground" />
            )}
          </Button>

          {/* Floating Label */}
          <label
            className={cn(
              "pointer-events-none absolute transition-all duration-200 ease-in-out",
              isRTL ? "right-3" : "left-3",
              Icon && (isRTL ? "right-10" : "left-10"),
              isLabelFloating
                ? "bg-background text-primary -top-2 px-1 text-xs"
                : "text-muted-foreground top-1/2 -translate-y-1/2 text-base",
            )}
          >
            {label}
          </label>
        </div>

        {/* Error Message */}
        {error && <p className="text-destructive mt-1 text-xs">{error}</p>}
      </div>
    );
  },
);

FloatingPasswordInput.displayName = "FloatingPasswordInput";

export { FloatingPasswordInput };
