"use client";

import logoImage from "@/../public/logo.png";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuthDialogStore } from "@/store/authDialogStore";
import { useAuthStore } from "@/store/authState";
import {
  Building,
  Building2,
  ChevronDown,
  Home,
  Info,
  LogIn,
  LogOut,
  MessageCircle,
  Settings,
  User,
  UserPlus,
} from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import messages from "../../../messages/en.json";
import AuthDialog from "../auth/AuthDialog";
import LangSwitcher from "./LangSwitcher";
import MobileMenu from "./MobileMenu";
import { ThemeSwitcher } from "./ThemeSwitcher";

export const mainNavItems = [
  { key: "home", icon: Home, link: "/" },
  { key: "corporateStay", icon: Building, link: "/corporate" },
  { key: "realEstate", icon: Building2, link: "/real-estate" },
  { key: "contact", icon: MessageCircle, link: "/contact" },
];

export const aboutNavItems = [
  { key: "ourStory", icon: Building2, link: "/about/story" },
  { key: "howItWorks", icon: Info, link: "/about/how-it-works" },
  { key: "careers", icon: Building, link: "/about/careers" },
];

const Header = () => {
  const tNav = useTranslations("nav");
  const tAuth = useTranslations("auth");
  const language = useLocale();
  const isRTL = language === "ar" ? true : false;
  const logout = useAuthStore((state) => state.logout);
  const user = useAuthStore((state) => state.user);
  const isAuthenticated = !!user;

  const openAuthDialog = useAuthDialogStore((state) => state.openDialog);

  return (
    <header className="bg-background sticky top-0 z-50 w-full shadow-xs backdrop-blur-md">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex shrink-0 items-center">
            <Link href="/" className="group flex cursor-pointer items-center">
              <Image
                priority
                src={logoImage}
                alt="Hala Logo"
                className="h-12 w-12 object-contain transition-transform duration-300 group-hover:scale-105"
              />
            </Link>
          </div>

          {/* Navigation - Desktop */}
          <nav className="hidden items-center gap-1 lg:flex">
            {mainNavItems.map(({ key, icon: Icon, link }) => (
              <Link key={key} href={link || "#"}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="rounded-lg px-3 py-2 transition-colors hover:bg-[#279fc7]/10 hover:text-[#279fc7]"
                >
                  <Icon className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {tNav(`${key as keyof typeof messages.nav}`)}
                </Button>
              </Link>
            ))}

            {/* About Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="rounded-lg px-3 py-2 transition-colors hover:bg-[#279fc7]/10 hover:text-[#279fc7]"
                >
                  <Info className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                  {tNav("about")}
                  <ChevronDown
                    className={`h-3 w-3 ${isRTL ? "mr-1" : "ml-1"}`}
                  />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="shadow-elevated animate-in fade-in-0 zoom-in-95 z-50 w-56 rounded-xl border p-2 backdrop-blur-xl duration-200">
                {aboutNavItems.map(({ icon: Icon, ...item }) => (
                  <Link key={item.key} href={item.link}>
                    <DropdownMenuItem
                      className={`group luxury-hover hover:shadow-soft flex cursor-pointer items-center rounded-lg px-4 py-3 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                        isRTL ? "flex-row-reverse" : ""
                      }`}
                    >
                      <div className="bg-brand-teal/20 flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br transition-all duration-300 group-hover:shadow-sm">
                        <Icon className="text-brand-teal group-hover:text-brand-teal-dark h-4 w-4 transition-colors duration-300" />
                      </div>
                      <span
                        className={`${
                          isRTL ? "mr-3" : "ml-3"
                        } group-hover:text-brand-teal font-medium transition-colors duration-300`}
                      >
                        {tNav(`${item.key as keyof typeof messages.nav}`)}
                      </span>
                    </DropdownMenuItem>
                  </Link>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center gap-1">
            <ThemeSwitcher />
            <LangSwitcher />

            {/* Auth Buttons - Desktop */}
            <div className="hidden items-center gap-1 md:flex">
              {isAuthenticated ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="rounded-lg px-3 py-2 transition-colors hover:bg-[#279fc7]/10 hover:text-[#279fc7]"
                    >
                      <div className="flex items-center space-x-2">
                        {/* {user?.avatar ? (
                          <img
                            src={user.avatar}
                            alt={user.name}
                            className="h-6 w-6 rounded-full object-cover"
                          />
                        ) : (
                        )} */}
                        <User className="h-4 w-4" />
                        <span className="hidden sm:inline">
                          {user?.firstName[0] + " " + user?.lastName[0]}
                        </span>
                        <ChevronDown className="h-3 w-3" />
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="shadow-elevated animate-in fade-in-0 zoom-in-95 z-50 w-56 rounded-xl border border-white/20 p-2 backdrop-blur-xl duration-200">
                    <Link href="/profile">
                      <DropdownMenuItem
                        className={`group luxury-hover hover:shadow-soft flex cursor-pointer items-center rounded-lg px-4 py-3 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                          isRTL ? "flex-row-reverse" : ""
                        }`}
                      >
                        <div className="bg-brand-teal/20 flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br transition-all duration-300 group-hover:shadow-sm">
                          <User className="text-brand-teal group-hover:text-brand-teal-dark h-4 w-4 transition-colors duration-300" />
                        </div>
                        <span
                          className={`${
                            isRTL ? "mr-3" : "ml-3"
                          } group-hover:text-brand-teal font-medium transition-colors duration-300`}
                        >
                          {tAuth("profile")}
                        </span>
                      </DropdownMenuItem>
                    </Link>
                    <DropdownMenuItem
                      className={`group luxury-hover hover:shadow-soft flex cursor-pointer items-center rounded-lg px-4 py-3 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                        isRTL ? "flex-row-reverse" : ""
                      }`}
                    >
                      <div className="bg-brand-teal/20 flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br transition-all duration-300 group-hover:shadow-sm">
                        <Settings className="text-brand-teal group-hover:text-brand-teal-dark h-4 w-4 transition-colors duration-300" />
                      </div>
                      <span
                        className={`${
                          isRTL ? "mr-3" : "ml-3"
                        } group-hover:text-brand-teal font-medium transition-colors duration-300`}
                      >
                        {tAuth("settings")}
                      </span>
                    </DropdownMenuItem>
                    <div className="my-2 h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent opacity-60" />
                    <DropdownMenuItem
                      onClick={logout}
                      className={`group luxury-hover hover:shadow-soft flex cursor-pointer items-center rounded-lg px-4 py-3 transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                        isRTL ? "flex-row-reverse" : ""
                      }`}
                    >
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-red-300/70 transition-all duration-300 group-hover:shadow-sm">
                        <LogOut className="h-4 w-4 text-red-500 transition-colors duration-300 group-hover:text-red-600" />
                      </div>
                      <span
                        className={`${
                          isRTL ? "mr-3" : "ml-3"
                        } font-medium text-red-600 transition-colors duration-300 group-hover:text-red-600`}
                      >
                        {tAuth("signOut")}
                      </span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="rounded-lg px-3 py-2 transition-colors hover:bg-[#279fc7]/10 hover:text-[#279fc7]"
                    onClick={() => openAuthDialog("signin")}
                  >
                    <LogIn className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
                    {tNav("login")}
                  </Button>
                  <Button
                    size="sm"
                    className="rounded-lg bg-[#279fc7] px-4 py-2 text-white shadow-sm transition-colors hover:bg-[#279fc7]/90"
                    onClick={() => openAuthDialog("signup")}
                  >
                    <UserPlus
                      className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`}
                    />
                    {tNav("signup")}
                  </Button>
                </>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="lg:hidden">
              <MobileMenu />
            </div>
          </div>
        </div>
      </div>

      {/* Login Modal */}
      <AuthDialog />
    </header>
  );
};

export default Header;
