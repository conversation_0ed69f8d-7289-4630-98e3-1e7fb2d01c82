import { useTranslations } from "next-intl";
import { Card, CardContent } from "@/components/ui/card";
import { getTranslations } from "next-intl/server";

interface TeamMember {
  name: string;
  role: string;
  image: string;
}

const TeamSection: React.FC = async () => {
  const t = await getTranslations();

  const team: TeamMember[] = [
    {
      name: "<PERSON><PERSON><PERSON>",
      role: "Co-founder",
      image: "/images/team/member1.jpg",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Co-founder",
      image: "/images/team/member2.jpg",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Co-founder",
      image: "/images/team/member3.jpg",
    },
    {
      name: "<PERSON><PERSON>",
      role: "Co-founder",
      image: "/images/team/member4.jpg",
    },
  ];

  return (
    <section className="from-brand-warm/20 to-background bg-gradient-to-br py-20">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            Our Team
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
            We are a team of passionate people who are dedicated to making a
            difference in the world through technology. We are committed to
            delivering exceptional products and services that enhance the lives
            of our customers.
          </p>
        </div>
        <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
          {team.map((member, index) => (
            <Card
              key={index}
              className="shadow-soft border-0 bg-transparent text-center transition-all duration-300 hover:scale-105"
            >
              <CardContent className="p-6">
                <div className="mb-4 overflow-hidden rounded-full">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="h-full w-full object-cover"
                  />
                </div>
                <h3 className="text-foreground mb-1 text-lg font-bold">
                  {member.name}
                </h3>
                <p className="text-primary text-sm font-semibold">
                  {member.role}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TeamSection;
