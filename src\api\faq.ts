import api from "@/lib/axios";

const BASE_ROUTE = "/FAQ";

interface FAQ {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  questionEn: string;
  questionAr: string;
  answerAr: string;
  answerEn: string;
  createdBy: string;
  createdAt: string;
}

interface Data {
  items: FAQ[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

interface GetFAQsResponse {
  message: string;
  data: Data;
}

interface GetFAQParams {
  pageNumber?: number;
  pageSize?: number;
}

export const getFAQsApi = async ({
  pageNumber = 1,
  pageSize = 10,
}: GetFAQParams = {}) => {
  const { data } = await api.get<GetFAQsResponse>(BASE_ROUTE, {
    params: { pageNumber, pageSize },
  });
  return data;
};

export const getFAQByIdApi = async (id: string) => {
  const { data } = await api.get<GetFAQsResponse>(`${BASE_ROUTE}/${id}`);
  return data;
};

export const createFAQApi = async (faq: Omit<FAQ, "id">) => {
  const { data } = await api.post<GetFAQsResponse>(BASE_ROUTE, faq);
  return data;
};

export const updateFAQApi = async (id: string, faq: Partial<FAQ>) => {
  const { data } = await api.put<GetFAQsResponse>(`${BASE_ROUTE}/${id}`, faq);
  return data;
};

export const deleteFAQApi = async (id: string) => {
  const { data } = await api.delete<GetFAQsResponse>(`${BASE_ROUTE}/${id}`);
  return data;
};
