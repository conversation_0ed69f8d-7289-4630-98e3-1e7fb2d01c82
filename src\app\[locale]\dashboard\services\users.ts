import api from "@/lib/axios";
import { AxiosError } from "axios";
import { PaginationData } from "../types/global";
import { User, UserStatus } from "../types/users";

export type GetUsersParams = {
  pageNumber: number;
  pageSize: number;
  status?: UserStatus;
};

type GetUsersResponse = { items: User[] } & PaginationData;

export const getUsers = async ({
  pageNumber = 1,
  pageSize = 10,
  status,
}: GetUsersParams): Promise<GetUsersResponse> => {
  try {
    const res = await api<GetUsersResponse>("/Users/<USER>", {
      params: {
        pageNumber,
        pageSize,
        ...(status !== undefined ? { status } : {}),
      },
    });

    return res.data;
  } catch (err) {
    if (err instanceof AxiosError) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};

export const deleteUser = async (id: string) => {
  try {
    const res = await api.delete(`/Users/<USER>
    return res.data;
  } catch (err) {
    console.error(err);

    if (err instanceof AxiosError) {
      if (err.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (err.response?.status === 401 || err.response?.status === 403) {
        throw new Error("غير مصرح به (المتصل غير مُصادق عليه كمسؤول)");
      }

      if (err.response?.status === 404) {
        throw new Error("غير موجود");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};
