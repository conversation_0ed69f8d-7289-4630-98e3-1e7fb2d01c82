import { create } from "zustand";
import axios from "axios";

const useFAQStore = create((set) => ({
  faqs: [],
  loading: false,
  error: null,

  // Get all FAQs
  getAllFAQs: async () => {
    set({ loading: true });
    try {
      const response = await axios.get("/api/FAQ");
      set({ faqs: response.data, loading: false, error: null });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  },

  // Get FAQ by ID
  getFAQById: async (id) => {
    set({ loading: true });
    try {
      const response = await axios.get(`/api/FAQ/${id}`);
      set({ loading: false, error: null });
      return response.data;
    } catch (error) {
      set({ error: error.message, loading: false });
      return null;
    }
  },

  // Create new FAQ (Admin only)
  createFAQ: async (faqData) => {
    set({ loading: true });
    try {
      const response = await axios.post("/api/FAQ", faqData);
      set((state) => ({
        faqs: [...state.faqs, response.data],
        loading: false,
        error: null,
      }));
      return response.data;
    } catch (error) {
      set({ error: error.message, loading: false });
      return null;
    }
  },

  // Update FAQ (Admin only)
  updateFAQ: async (id, faqData) => {
    set({ loading: true });
    try {
      const response = await axios.put(`/api/FAQ/${id}`, faqData);
      set((state) => ({
        faqs: state.faqs.map((faq) => (faq.id === id ? response.data : faq)),
        loading: false,
        error: null,
      }));
      return response.data;
    } catch (error) {
      set({ error: error.message, loading: false });
      return null;
    }
  },

  // Delete FAQ (Admin only)
  deleteFAQ: async (id) => {
    set({ loading: true });
    try {
      await axios.delete(`/api/FAQ/${id}`);
      set((state) => ({
        faqs: state.faqs.filter((faq) => faq.id !== id),
        loading: false,
        error: null,
      }));
      return true;
    } catch (error) {
      set({ error: error.message, loading: false });
      return false;
    }
  },
}));

export default useFAQStore;
