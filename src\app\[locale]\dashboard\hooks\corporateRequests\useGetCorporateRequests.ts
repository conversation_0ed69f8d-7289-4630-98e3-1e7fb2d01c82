import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { getCorporateRequests } from "../../services/corporateRequests";
import { CorporateRequestStatus } from "../../types/corporateRequests";

export default function useGetCorporateRequests() {
  const searchParams = useSearchParams();
  const pageNumber = Number(searchParams.get("pageNumber")) || 1;
  const pageSize = Number(searchParams.get("pageSize")) || 10;
  const statusParam = searchParams.get("status");
  const status: CorporateRequestStatus | undefined = (() => {
    if (statusParam === null) return undefined;
    const parsed = Number(statusParam);
    return Number.isNaN(parsed)
      ? undefined
      : (parsed as CorporateRequestStatus);
  })();

  return useQuery({
    queryKey: ["corporate-requests", pageNumber, pageSize, status],
    queryFn: () =>
      getCorporateRequests({
        pageNumber,
        pageSize,
        ...(status !== undefined ? { status } : {}),
      }),
    staleTime: 1000 * 60 * 2, // 2 minutes
    retry: 3,
    retryDelay: 1000,
  });
}
