import corporateBanner from "@/../public/CorporateStay/corporate_banner.webp";
import { Building2 } from "lucide-react";
import { AppointmentForm } from "./AppointmentForm";
import { getTranslations } from "next-intl/server";
import Image from "next/image";
import ScrollRevealSection from "@/components/ScrollReveal";
import { Badge } from "@/components/ui/badge";

const HeroSection: React.FC = async () => {
  const t = await getTranslations("realEstate.hero");

  return (
    <section className="relative flex h-[80vh] items-center justify-center overflow-hidden text-center">
      <div className="absolute inset-0">
        <Image
          src={corporateBanner}
          alt="Corporate Stay Hero"
          className="h-full w-full object-cover"
          width={1920}
          height={1080}
        />
        <div className="absolute inset-0 bg-black/60" />
      </div>

      <ScrollRevealSection>
        <div className="relative z-10 mx-auto max-w-7xl px-4 text-center text-white sm:px-6 lg:px-8">
          <Badge className="mb-6 rounded-full border-white/30 bg-slate-600/30 px-6 py-2 text-sm font-semibold text-white transition-all duration-300 md:text-base">
            ✨ {t("subtitle")}
          </Badge>

          <h1 className="animate-fade-in mb-6 text-4xl leading-tight font-bold md:text-6xl lg:text-7xl">
            {t("title")}
          </h1>

          <p className="animate-fade-in-delay mx-auto mb-8 max-w-3xl text-base leading-relaxed opacity-90 md:text-2xl">
            {t("description")}
          </p>

          <div className="flex flex-col items-center justify-center gap-6 sm:flex-row">
            <AppointmentForm buttonClassName="bg-primary text-white hover:border-primary hover:text-primary" />
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 h-20 w-20 animate-pulse rounded-full bg-white/5 blur-xl" />
        <div className="bg-primary/20 absolute right-10 bottom-20 h-32 w-32 animate-pulse rounded-full blur-2xl delay-1000" />
      </ScrollRevealSection>
    </section>
  );
};

export default HeroSection;
