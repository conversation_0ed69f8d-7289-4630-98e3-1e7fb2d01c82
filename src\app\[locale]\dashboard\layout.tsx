import { ThemeSwitcher } from "@/components/header/ThemeSwitcher";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { redirect } from "@/i18n/navigation";
import { Locale } from "next-intl";
import { AppSidebar } from "./components/app-sidebar";
import RouteGuard from "./RouteGuard";
import { NavUser } from "./components/nav-user";

import "./globals.css";

export default async function DashboardLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}) {
  const locale = (await params).locale;

  if (locale !== "ar") {
    redirect({ href: "/dashboard", locale: "ar", forcePrefix: true });
  }

  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body>
        <RouteGuard>
          <SidebarProvider className="">
            <AppSidebar
              variant="inset"
              side="right"
              className="border-e py-0"
            />
            <SidebarInset className="max-w-full overflow-x-auto !rounded-none">
              <header className="bg-secondary sticky top-0 z-[2] flex h-15 w-full shrink-0 items-center gap-2 py-2">
                <div className="flex w-full items-center justify-between gap-2 px-4">
                  <div className="flex grow items-center gap-2">
                    <SidebarTrigger className="-ms-1" />
                    <Separator
                      orientation="vertical"
                      className="mr-2 data-[orientation=vertical]:h-4"
                    />
                    <ThemeSwitcher size="icon" />
                  </div>

                  <NavUser />
                </div>
              </header>
              <main className="mt-4 flex flex-1 flex-col gap-4 p-4">
                {children}
              </main>
            </SidebarInset>
          </SidebarProvider>
        </RouteGuard>
      </body>
    </html>
  );
}
