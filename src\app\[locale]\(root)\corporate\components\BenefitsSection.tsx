import costSavingImg from "@/../public/CorporateStay/cost_saving.webp";
import flexibleSolutionsImg from "@/../public/CorporateStay/Flexible-solutions.webp";
import increaseProductivityImg from "@/../public/CorporateStay/increase_productivity.webp";
import { DollarSign, LucideProps, Settings, TrendingUp } from "lucide-react";
import { getTranslations } from "next-intl/server";
import Image, { StaticImageData } from "next/image";

interface Benefit {
  icon: React.ComponentType<LucideProps>;
  title: string;
  description: string;
  image: StaticImageData;
}

const BenefitsSection: React.FC = async () => {
  const t = await getTranslations("corporate");

  const benefits: Benefit[] = [
    {
      icon: DollarSign,
      title: t("whyStella.costSaving.title"),
      description: t("whyStella.costSaving.description"),
      image: costSavingImg,
    },
    {
      icon: TrendingUp,
      title: t("whyStella.productivity.title"),
      description: t("whyStella.productivity.description"),
      image: increaseProductivityImg,
    },
    {
      icon: Settings,
      title: t("whyStella.flexibility.title"),
      description: t("whyStella.flexibility.description"),
      image: flexibleSolutionsImg,
    },
  ];

  return (
    <section className="bg-muted/30 py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("whyStella.title")}
          </h2>
        </div>

        <div className="space-y-16">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className={`grid items-center gap-12 lg:grid-cols-2 ${
                index % 2 === 1 ? "lg:grid-flow-col-dense" : ""
              }`}
            >
              <div className={index % 2 === 1 ? "lg:col-start-2" : ""}>
                <div className="mb-6 flex items-center">
                  <div className="me-4 flex h-12 w-12 items-center justify-center rounded-xl bg-[#279fc7] shadow-lg">
                    <benefit.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-foreground text-2xl font-bold md:text-3xl">
                    {benefit.title}
                  </h3>
                </div>
                <p className="text-muted-foreground text-lg leading-relaxed md:text-xl">
                  {benefit.description}
                </p>
              </div>
              <div className={index % 2 === 1 ? "lg:col-start-1" : ""}>
                <Image
                  src={benefit.image}
                  alt={benefit.title}
                  className="h-auto w-full rounded-2xl shadow-xl transition-transform duration-300 hover:scale-105"
                  width={750}
                  height={750}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
