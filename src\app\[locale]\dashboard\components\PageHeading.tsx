import { cn } from "@/lib/utils";
import { JSX } from "react";

interface PageHeadingProps {
  children: React.ReactNode | React.ReactNode[];
  as?: keyof JSX.IntrinsicElements;
  className?: string;
}

export default function PageHeading({
  as,
  children,
  className,
}: PageHeadingProps) {
  const Component = as || "h1";

  return (
    <Component
      className={cn(
        "text-primary border-b-primary border-b-2 pb-2 text-base font-bold md:border-b-4 md:text-2xl",
        className,
      )}
    >
      {children}
    </Component>
  );
}
