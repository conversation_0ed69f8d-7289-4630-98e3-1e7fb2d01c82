import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronRight } from "lucide-react";

const HeroSection: React.FC = () => {
  const t = useTranslations("careers.hero");

  return (
    <section className="from-primary/10 via-brand-ocean/5 to-background relative overflow-hidden bg-gradient-to-br py-20 lg:py-32">
      <div className="bg-grid-pattern absolute inset-0 opacity-5"></div>
      <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <Badge className="bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 mb-6 transition-colors">
            {t("badge")}
          </Badge>
          <h1 className="text-foreground mb-6 text-4xl leading-tight font-bold md:text-6xl lg:text-7xl">
            {t("title")}
          </h1>
          <p className="text-muted-foreground mb-8 text-xl leading-relaxed">
            {t("subtitle")}
          </p>
          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <Button
              size="lg"
              className="bg-primary hover:bg-primary/90 rounded-full px-8 py-4 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105"
            >
              {t("viewJobs")}
              <ChevronRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-primary text-primary hover:bg-primary rounded-full border-2 px-8 py-4 font-semibold transition-all duration-300 hover:text-white"
            >
              {t("learnMore")}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
