"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { CorporateRequestStatus } from "../../types/corporateRequests";
import { cn } from "@/lib/utils";

export const statusLabels: Record<CorporateRequestStatus, string> = {
  [CorporateRequestStatus.NotApproved]: "غير معتمد",
  [CorporateRequestStatus.Pending]: "معلق",
  [CorporateRequestStatus.Approved]: "معتمد",
  [CorporateRequestStatus.Rejected]: "مرفوض",
};

export const statusClasses: Record<CorporateRequestStatus, string> = {
  [CorporateRequestStatus.NotApproved]:
    "bg-gray-100 text-gray-800 hover:!bg-gray-200 hover:!text-gray-800",
  [CorporateRequestStatus.Pending]:
    "bg-yellow-100 text-yellow-800 hover:!bg-yellow-200 hover:!text-gray-800",
  [CorporateRequestStatus.Approved]:
    "bg-green-100 text-green-800 hover:!bg-green-200 hover:!text-gray-800",
  [CorporateRequestStatus.Rejected]:
    "bg-red-100 text-red-800 hover:!bg-red-200 hover:!text-gray-800",
};

export default function CorporateFilterByStatus() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const statusParam = searchParams.get("status");
  const statusValue = statusParam !== null ? statusParam : "all";

  return (
    <div className="me-auto flex items-center gap-2">
      <span className="text-sm">الحالة:</span>
      <Select
        dir="rtl"
        value={statusValue}
        onValueChange={(v) => {
          const params = new URLSearchParams(searchParams.toString());

          if (v === "all") {
            params.delete("status");
          } else {
            params.set("status", v);
          }

          // reset to first page when filter changes
          params.set("pageNumber", "1");

          router.push(`${pathname}?${params.toString()}`, {
            scroll: false,
          });
        }}
      >
        <SelectTrigger>
          <SelectValue placeholder="اختر الحالة" />
        </SelectTrigger>
        <SelectContent align="start">
          <SelectItem value="all">الكل</SelectItem>
          {Object.entries(statusLabels).map(([key, value]) => (
            <SelectItem
              className={cn(
                "mt-1 hover:!bg-transparent",
                statusClasses[key as unknown as CorporateRequestStatus],
              )}
              key={key}
              value={String(key)}
            >
              <span>{value}</span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
