"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Trash2 } from "lucide-react";
import { useState } from "react";
import useDeleteContactRequest from "../../hooks/contactRequests/useDeleteContactRequest";
import { ConfirmDialog } from "../../components/ConfirmDialog";

interface DeleteContactRequestButtonProps {
  id: string;
}

export default function DeleteContactRequestButton({
  id,
}: DeleteContactRequestButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { mutate: deleteRequest, isPending } = useDeleteContactRequest();

  const handleDelete = () => {
    deleteRequest(id, {
      onSuccess: () => setIsOpen(false),
    });
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="destructive"
            size="icon"
            className="rounded-full"
            onClick={() => setIsOpen(true)}
            disabled={isPending}
          >
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">حذف</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent variant="destructive">حذف الطلب</TooltipContent>
      </Tooltip>

      {isOpen && (
        <ConfirmDialog
          title="هل أنت متأكد من حذف هذا الطلب؟"
          description="سيتم حذف هذا الطلب نهائياً. لا يمكن التراجع عن هذا الإجراء."
          confirmText="حذف"
          cancelText="إلغاء"
          destructive
          isLoading={isPending}
          onConfirmAction={handleDelete}
          onCancelAction={() => setIsOpen(false)}
        />
      )}
    </>
  );
}
