import ContactHero from "@/app/[locale]/(root)/contact/components/ContactHero";
import ContactMethods from "@/app/[locale]/(root)/contact/components/ContactMethods";
import SupportFeatures from "@/app/[locale]/(root)/contact/components/SupportFeatures";
import ScrollRevealSection from "@/components/ScrollReveal";
import { ContactForm } from "./components";

const ContactUs: React.FC = () => {
  return (
    <div className="bg-background text-foreground min-h-screen">
      <ContactHero />

      <ScrollRevealSection>
        <ContactMethods />
      </ScrollRevealSection>

      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid items-start gap-16 lg:grid-cols-2">
            <ScrollRevealSection>
              <ContactForm />
            </ScrollRevealSection>

            <ScrollRevealSection>
              <SupportFeatures />
            </ScrollRevealSection>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactUs;
