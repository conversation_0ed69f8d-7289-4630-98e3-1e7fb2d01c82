"use client";

import GenericPageLoading from "@/components/GenericPageLoading";
import ApiError from "../../components/ApiError";
import { DataTable } from "../../components/data-table";
import GenericStatusFilter from "../../components/GenericStatusFilter";
import useGetUsersQuery from "../../hooks/users/useGetUsers";
import { usersColumns } from "./UsersColumns";

export default function UsersData() {
  const { data, isLoading, isError, error } = useGetUsersQuery();

  if (isLoading) {
    return <GenericPageLoading />;
  }

  if (isError || !data) {
    return <ApiError error={error as Error} />;
  }

  return (
    <div>
      <DataTable
        columns={usersColumns}
        data={data.items}
        pagination={{
          pageNumber: data.pageNumber,
          totalPages: data.totalPages,
          hasPreviousPage: data.hasPreviousPage,
          hasNextPage: data.hasNextPage,
        }}
        beforeTable={<GenericStatusFilter />}
      />
    </div>
  );
}
