"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { LucideIcon, SendIcon } from "lucide-react";
import { useTranslations } from "next-intl";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  icon?: LucideIcon;
  title?: string;
  description?: string;
}

export default function FormFeedback({
  open,
  onOpenChange,
  icon,
  title,
  description,
}: Props) {
  const t = useTranslations("formFeedback");

  const Icon = icon || SendIcon;
  const tTitle = title || t("title");
  const tDescription = description || t("description");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-8 sm:max-w-[480px]">
        <div className="flex min-h-[300px] flex-col items-center justify-center gap-4">
          <div className="grid aspect-square place-content-center rounded-full bg-green-500/30 p-4 text-4xl">
            <Icon className="h-12 w-12 text-green-500" />
          </div>
          <DialogHeader>
            <DialogTitle asChild>
              <h2 className="text-center text-2xl font-bold md:text-3xl">
                {tTitle}
              </h2>
            </DialogTitle>
            <DialogDescription asChild>
              <p className="text-muted-foreground text-center text-base md:text-lg">
                {tDescription}
              </p>
            </DialogDescription>
          </DialogHeader>
        </div>
      </DialogContent>
    </Dialog>
  );
}
