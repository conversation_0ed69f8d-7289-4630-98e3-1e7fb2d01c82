"use client";

import { Mail } from "lucide-react";
import { Input } from "./input";
import { cn } from "@/lib/utils";
import { useLocale } from "next-intl";

export default function EmailInput({
  className,
  ...props
}: React.ComponentProps<typeof Input>) {
  const locale = useLocale();

  return (
    <div dir="ltr" lang="en" className="relative">
      <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
      <Input
        type="email"
        className={cn(
          "pl-10",
          {
            "placeholder:text-right": locale === "ar",
            "placeholder:text-left": locale === "en",
          },
          className,
        )}
        {...props}
      />
    </div>
  );
}
