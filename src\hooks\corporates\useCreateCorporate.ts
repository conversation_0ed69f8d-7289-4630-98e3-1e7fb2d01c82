import { createCorporatePricingRequestApi } from "@/api/corporate";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { isAxiosError } from "axios";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

export const useCreateCorporate = () => {
  const t = useTranslations("errors");
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCorporatePricingRequestApi,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["corporatePricingRequests"] });
    },
    onError: (error) => {
      console.error(error);
      if (!isAxiosError(error)) toast.error(t("generalError"));
    },
  });
};
