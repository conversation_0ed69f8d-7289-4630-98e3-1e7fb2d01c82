import { <PERSON><PERSON> } from "@/components/ui/button";
import { MessageCircle, ArrowRight } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";

const ContactSupportSection: React.FC = () => {
  const t = useTranslations();

  return (
    <div className="mt-12 text-center">
      <div className="from-primary/5 via-primary/10 to-primary/5 rounded-2xl bg-gradient-to-r p-8">
        <MessageCircle className="text-primary mx-auto mb-4 h-12 w-12" />
        <h3 className="text-foreground mb-2 text-xl font-bold">
          {t("faq.stillHaveQuestions")}
        </h3>
        <p className="text-muted-foreground mb-6">
          {t("faq.supportDescription")}
        </p>
        <div className="flex justify-center">
          <Link href="/contact">
            <Button
              variant="outline"
              className="border-primary text-primary hover:bg-primary transition-all duration-300 hover:text-white"
            >
              {t("faq.contactSupport")}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ContactSupportSection;
