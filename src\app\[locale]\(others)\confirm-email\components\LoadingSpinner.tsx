import { Loader2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslations } from "next-intl";

interface LoadingSpinnerProps {
  message?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ message }) => {
  const t = useTranslations();
  const displayMessage = message || t("auth.loading") || "Loading...";

  return (
    <Card className="shadow-elevated bg-background/95 w-full border-0 backdrop-blur-sm">
      <CardContent className="p-6 text-center">
        <div className="mb-6 flex justify-center">
          <div className="bg-primary/10 flex h-16 w-16 items-center justify-center rounded-full">
            <Loader2 className="text-primary h-8 w-8 animate-spin" />
          </div>
        </div>
        <p className="text-muted-foreground text-sm">{displayMessage}</p>
      </CardContent>
    </Card>
  );
};

export default LoadingSpinner;
