import corporateBanner from "@/../public/CorporateStay/corporate_banner.webp";
import ScrollRevealSection from "@/components/ScrollReveal";
import { Badge } from "@/components/ui/badge";
import { getTranslations } from "next-intl/server";
import Image from "next/image";
import { CorporateForm } from "./CorporateForm";

const HeroSection: React.FC = async () => {
  const t = await getTranslations("corporate");

  return (
    <section className="relative flex min-h-[80vh] items-center justify-center overflow-hidden">
      <div className="absolute inset-0">
        <Image
          src={corporateBanner}
          alt="Corporate Stay Hero"
          className="h-full w-full object-cover"
          width={1920}
          height={1080}
        />
        <div className="absolute inset-0 bg-black/60" />
      </div>

      <ScrollRevealSection>
        <div className="relative z-10 mx-auto max-w-7xl px-4 text-center text-white sm:px-6 lg:px-8">
          <Badge className="mb-6 rounded-full border-white/30 bg-slate-600/30 px-6 py-2 text-sm font-semibold text-white transition-all duration-300 md:text-base">
            ✨ {t("hero.subtitle")}
          </Badge>

          <h1 className="animate-fade-in mb-6 text-4xl leading-tight font-bold md:text-6xl lg:text-7xl">
            <span className="block">{t("hero.title")}</span>
          </h1>

          <p className="animate-fade-in-delay mx-auto mb-8 max-w-3xl text-base leading-relaxed opacity-90 md:text-2xl">
            {t("hero.subtitle")}
          </p>

          <CorporateForm buttonClassName="bg-primary text-white hover:border-primary hover:text-primary" />
        </div>
      </ScrollRevealSection>
    </section>
  );
};

export default HeroSection;
