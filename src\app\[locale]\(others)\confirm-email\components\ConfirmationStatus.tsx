"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Check<PERSON>ircle, XCircle, Loader2, Mail, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";

interface ConfirmationStatusProps {}

type ConfirmationState = "loading" | "success" | "error" | "invalid";

const ConfirmationStatus: React.FC<ConfirmationStatusProps> = ({}) => {
  const searchParams = useSearchParams();
  const email = searchParams.get("email") || "";
  const token = searchParams.get("token") || "";

  const [status, setStatus] = useState<ConfirmationState>("loading");
  const [countdown, setCountdown] = useState(5);
  const router = useRouter();
  const t = useTranslations();

  useEffect(() => {
    const confirmEmail = async () => {
      try {
        const response = await fetch(
          "https://hala-reservation.runasp.net/api/Auth/confirm-email",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, token }),
          },
        );

        if (response.ok) {
          setStatus("success");
          // Start countdown for redirect
          const timer = setInterval(() => {
            setCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(timer);
                const searchParams = new URLSearchParams({
                  email,
                });

                router.push("/?" + searchParams.toString());
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        } else {
          console.error("Email confirmation failed");
          setStatus("error");
        }
      } catch (error) {
        console.error("Email confirmation error:", error);
        setStatus("error");
      }
    };

    if (email && token) {
      confirmEmail();
    } else {
      setStatus("invalid");
    }
  }, [email, token, router]);

  const getStatusIcon = () => {
    switch (status) {
      case "loading":
        return <Loader2 className="text-primary h-8 w-8 animate-spin" />;
      case "success":
        return <CheckCircle className="h-8 w-8 text-green-600" />;
      case "error":
        return <XCircle className="h-8 w-8 text-red-600" />;
      case "invalid":
        return <Mail className="h-8 w-8 text-gray-500" />;
      default:
        return null;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case "loading":
        return {
          title: t("auth.confirmingEmail") || "Confirming Your Email",
          description:
            t("auth.confirmingEmailDescription") ||
            "Please wait while we verify your email address...",
        };
      case "success":
        return {
          title:
            t("auth.emailConfirmedSuccess") || "Email Confirmed Successfully!",
          description:
            t("auth.emailConfirmedSuccessMessage", { countdown }) ||
            `Welcome to Hala! Your email has been verified. Redirecting to home page in ${countdown} seconds...`,
        };
      case "error":
        return {
          title: t("auth.emailConfirmationFailed") || "Confirmation Failed",
          description:
            t("auth.emailConfirmationFailedMessage") ||
            "We couldn't verify your email. The link may be expired or invalid.",
        };
      case "invalid":
        return {
          title:
            t("auth.invalidConfirmationLink") || "Invalid Confirmation Link",
          description:
            t("auth.invalidConfirmationLinkMessage") ||
            "The confirmation link is missing required information.",
        };
      default:
        return { title: "", description: "" };
    }
  };

  const { title, description } = getStatusMessage();

  return (
    <Card className="shadow-elevated bg-background/95 w-full border-0 backdrop-blur-sm">
      <CardContent className="p-6 text-center">
        <div className="mb-6 flex justify-center">
          <div
            className={`flex h-16 w-16 items-center justify-center rounded-full ${
              status === "success"
                ? "bg-green-100"
                : status === "error"
                  ? "bg-red-100"
                  : status === "invalid"
                    ? "bg-gray-100"
                    : "bg-primary/10"
            }`}
          >
            {getStatusIcon()}
          </div>
        </div>

        <h2 className="text-foreground mb-4 text-xl font-bold">{title}</h2>

        <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
          {description}
        </p>

        {email && (
          <div className="bg-muted mb-6 rounded-lg p-3">
            <p className="text-muted-foreground text-sm">Email:</p>
            <p className="text-foreground font-medium">{email}</p>
          </div>
        )}

        {status === "error" && (
          <div className="space-y-3">
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="h-12 w-full"
            >
              {t("auth.tryAgain") || "Try Again"}
            </Button>
            <Button
              onClick={() => router.push("/")}
              className="bg-primary text-primary-foreground hover:bg-primary/90 h-12 w-full"
            >
              {t("auth.goToHome") || "Go to Home"}
            </Button>
          </div>
        )}

        {status === "invalid" && (
          <Button
            onClick={() => router.push("/")}
            className="bg-primary text-primary-foreground hover:bg-primary/90 h-12 w-full"
          >
            {t("auth.goToHome") || "Go to Home"}
          </Button>
        )}

        {status === "success" && (
          <div className="space-y-4">
            <div className="bg-muted h-2 w-full rounded-full">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-1000 ease-linear"
                style={{ width: `${((5 - countdown) / 5) * 100}%` }}
              ></div>
            </div>
            <Button
              onClick={() => router.push("/")}
              className="bg-primary text-primary-foreground hover:bg-primary/90 h-12 w-full"
            >
              <Shield className="mr-2 h-4 w-4" />
              {t("auth.continueToHome") || "Continue to Home"}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ConfirmationStatus;
