import { getTranslations } from "next-intl/server";

interface Milestone {
  year: string;
  title: string;
  description: string;
}

const MilestonesSection: React.FC = async () => {
  const t = await getTranslations();

  const milestones: Milestone[] = [
    {
      year: "2020",
      title: t("about.milestones.founded.title"),
      description: t("about.milestones.founded.description"),
    },
    {
      year: "2021",
      title: t("about.milestones.expansion.title"),
      description: t("about.milestones.expansion.description"),
    },
    {
      year: "2022",
      title: t("about.milestones.recognition.title"),
      description: t("about.milestones.recognition.description"),
    },
    {
      year: "2024",
      title: t("about.milestones.global.title"),
      description: t("about.milestones.global.description"),
    },
  ];

  return (
    <section className="bg-background py-20">
      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("about.title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg md:text-xl">
            {t("about.description")}
          </p>
        </div>
        <div className="relative">
          {/* Timeline line */}
          <div className="bg-border absolute top-0 left-1/2 hidden h-full w-0.5 -translate-x-1/2 md:block"></div>

          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div
                key={index}
                className={`relative flex items-center ${index % 2 === 0 ? "md:justify-start" : "md:justify-end"}`}
              >
                <div
                  className={`bg-primary/10 border-primary/10 rounded-2xl border-2 p-6 md:w-[calc(100%_/_2_-_40px)]`}
                >
                  <div className="bg-primary/20 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm md:text-base font-semibold">
                    {milestone.year}
                  </div>
                  <h3 className="text-foreground mb-2 text-xl font-bold md:text-2xl">
                    {milestone.title}
                  </h3>
                  <p className="text-muted-foreground text-base md:text-lg">
                    {milestone.description}
                  </p>
                </div>
                <div className="bg-primary absolute left-1/2 z-10 hidden h-4 w-4 -translate-x-1/2 rounded-full md:block" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default MilestonesSection;
