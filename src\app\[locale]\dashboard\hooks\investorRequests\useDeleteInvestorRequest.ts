import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { deleteInvestorRequest } from "../../services/investorRequests";

export default function useDeleteInvestorRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteInvestorRequest,
    onSuccess: () => {
      toast.success("تم حذف طلب المستثمر");
      queryClient.invalidateQueries({
        queryKey: ["investor-requests"],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
}
