import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { contactRequestReasons } from "@/constants/contactRequestReasons";
import { ColumnDef } from "@tanstack/react-table";
import { Eye } from "lucide-react";
import { useMessages } from "next-intl";
import {
  createSelectColumn,
  createTextColumn,
} from "../../components/data-table";
import {
  defaultStatusClasses,
  defaultStatusLabels,
} from "../../components/GenericStatusFilter";
import { formatDate } from "../../lib/formatters";
import { ContactRequest } from "../../types/contactRequest";
import { DefaultStatus } from "../../types/global";
import DeleteContactRequestButton from "./DeleteContactRequestButton";
import UpdateContactRequestStatusButton from "./UpdateContactRequestStatusButton";

const contactRequestsColumns: ColumnDef<ContactRequest>[] = [
  // createSelectColumn<ContactRequest>(),
  createTextColumn<ContactRequest>("fullName", "الاسم الكامل"),
  createTextColumn<ContactRequest>("email", "البريد الإلكتروني"),
  createTextColumn<ContactRequest>("phone", "رقم الهاتف"),
  {
    accessorKey: "message",
    meta: { arabicName: "الرسالة" },
    header: "الرسالة",
    cell: ({ row }) => {
      const message = (row.getValue("message") as string) || "";
      const max = 40;
      const isLong = message.length > max;
      const preview = isLong ? message.slice(0, max) + "…" : message;

      return (
        <div className="flex max-w-[200px] min-w-[150px] items-center gap-2 overflow-hidden text-wrap whitespace-pre-wrap">
          <span className="text-foreground/80 text-sm">{preview}</span>
          {isLong && (
            <Popover>
              <Tooltip>
                <TooltipTrigger asChild>
                  <PopoverTrigger asChild>
                    <Button
                      aria-label="عرض الرسالة كاملة"
                      variant="secondary"
                      size="icon"
                      className="rounded-full"
                    >
                      <Eye className="size-4" />
                      <span className="sr-only">عرض الرسالة</span>
                    </Button>
                  </PopoverTrigger>
                </TooltipTrigger>
                <TooltipContent variant="secondary">عرض الرسالة</TooltipContent>
              </Tooltip>
              <PopoverContent
                align="end"
                side="top"
                className="max-w-md text-sm leading-6 whitespace-pre-wrap"
              >
                {message}
              </PopoverContent>
            </Popover>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "reason",
    meta: { arabicName: "السبب" },
    header: "السبب",
    cell: ({ row }) => {
      const messages = useMessages();
      const reasonNumber = row.getValue("reason") as number;
      const reason = contactRequestReasons.find(
        (reason) => reason.value === reasonNumber,
      );

      return (
        messages.contact.form.reasons[
          (reason?.label
            .split(".")
            .at(-1) as keyof typeof messages.contact.form.reasons) ?? ""
        ] ?? "_"
      );
    },
  },
  {
    accessorKey: "status",
    meta: { arabicName: "الحالة" },
    header: "الحالة",
    cell: ({ row }) => {
      const status = row.getValue("status") as DefaultStatus;

      return (
        <span
          className={`rounded-full px-2 py-1 text-xs font-medium ${defaultStatusClasses[status]}`}
        >
          {defaultStatusLabels[status]}
        </span>
      );
    },
  },
  createTextColumn<ContactRequest>("createdAt", "تاريخ الإنشاء", {
    sortable: true,
    format(value) {
      return formatDate(new Date(value));
    },
  }),
  {
    id: "actions",
    header: "الإجراءات",
    cell: ({ row }) => {
      const request = row.original;
      return (
        <div className="flex items-center justify-start gap-1.5">
          <UpdateContactRequestStatusButton
            id={request.id}
            currentStatus={request.status}
          />
          <DeleteContactRequestButton id={request.id} />
        </div>
      );
    },
  },
];

export default contactRequestsColumns;
