"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import * as React from "react";

type ConfirmDialogProps = {
  title: React.ReactNode;
  isLoading: boolean;
  description?: React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  onConfirmAction: () => void | Promise<void>;
  onCancelAction: () => void;
  destructive?: boolean;
  className?: string;
};

export function ConfirmDialog({
  title,
  description,
  confirmText = "تأكيد",
  cancelText = "إلغاء",
  onConfirmAction,
  destructive = false,
  isLoading = false,
  className,
  onCancelAction,
}: ConfirmDialogProps) {
  const handleConfirm = () => {
    onConfirmAction?.();
  };

  return (
    <AlertDialog open>
      <AlertDialogContent className={className}>
        <AlertDialogHeader className="items-start py-4">
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description ?? "هذا الإجراء لا يمكن الرجوع فيه"}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="!justify-start">
          <AlertDialogCancel disabled={isLoading} onClick={onCancelAction}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              destructive
                ? buttonVariants({ variant: "destructive" })
                : undefined,
            )}
          >
            {isLoading ? "جاري التنفيذ..." : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
