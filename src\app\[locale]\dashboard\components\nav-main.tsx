"use client";

import {
  BadgeDollarSignIcon,
  Building2Icon,
  HomeIcon,
  LayoutDashboardIcon,
  PhoneIcon,
  Settings2,
  UsersIcon,
} from "lucide-react";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import Link from "next/link";
import { usePathname } from "next/navigation";

const navMainLinks = [
  {
    title: "نظرة عامة",
    url: "/dashboard",
    icon: LayoutDashboardIcon,
  },
  {
    title: "المستخدمين",
    url: "/dashboard/users",
    icon: UsersIcon,
  },
  {
    title: "طلبات الشركات",
    url: "/dashboard/corporate-requests",
    icon: Building2Icon,
  },
  {
    title: "طلبات التواصل",
    url: "/dashboard/contact-requests",
    icon: PhoneIcon,
  },
  {
    title: "طلبات العقارات للمستثمرين",
    url: "/dashboard/investor-requests",
    icon: BadgeDollarSignIcon,
  },
];

export function NavMain() {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return (
      pathname.split("/").slice(2).join("/") ===
      path.split("/").slice(1).join("/")
    );
  };

  return (
    <SidebarGroup>
      <SidebarMenu>
        {navMainLinks.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton asChild tooltip={item.title}>
              <Link
                href={item.url}
                className={isActive(item.url) ? "bg-sidebar-primary" : ""}
              >
                <item.icon />
                <span>{item.title}</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  );
}
