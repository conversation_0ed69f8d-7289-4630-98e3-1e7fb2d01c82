"use client";

import { Button } from "@/components/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import { useRouter, useSearchParams } from "next/navigation";

interface AppPaginationProps {
  pageNumber: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  className?: string;
}

export default function AppPagination({
  pageNumber,
  totalPages,
  hasPreviousPage,
  hasNextPage,
  className,
}: AppPaginationProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const setPage = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("pageNumber", page.toString());
    router.push(`?${params.toString()}`);
  };

  return (
    <Pagination dir="rtl" className={className}>
      <PaginationContent>
        <PaginationItem>
          <Button
            variant="ghost"
            onClick={() => hasPreviousPage && setPage(pageNumber - 1)}
            className={!hasPreviousPage ? "pointer-events-none opacity-50" : ""}
          >
            السابق
          </Button>
        </PaginationItem>

        {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
          <PaginationItem key={p}>
            <Button
              size="sm"
              onClick={() => setPage(p)}
              variant={p === pageNumber ? "default" : "outline"}
              className="tabular-nums"
            >
              {p}
            </Button>
          </PaginationItem>
        ))}

        <PaginationItem>
          <Button
            variant="ghost"
            onClick={() => hasNextPage && setPage(pageNumber + 1)}
            className={!hasNextPage ? "pointer-events-none opacity-50" : ""}
          >
            التالي
          </Button>
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
