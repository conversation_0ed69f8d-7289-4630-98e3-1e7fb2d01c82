import { useMutation, useQueryClient } from "@tanstack/react-query";
import { loginApi } from "@/api/auth";
import { useAuthStore } from "@/store/authState";

export const useLogin = () => {
  const queryClient = useQueryClient();
  const { setAuth } = useAuthStore();

  return useMutation({
    mutationFn: loginApi,
    onSettled: (data) => {
      console.log(data);
    },
    onSuccess: (data) => {
      if (data && data.token) {
        setAuth(
          {
            firstName: data.firstName,
            lastName: data.lastName,
            id: data.id,
            email: data.email,
          },
          data.token,
          data.expiresIn,
          data.refreshToken,
        );
      }
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
  });
};
