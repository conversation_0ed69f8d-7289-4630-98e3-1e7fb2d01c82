import { cn } from "@/lib/utils";

// Common card styling patterns used throughout the application
export const cardStyles = {
  // Base card styles
  base: "rounded-lg border bg-card text-card-foreground shadow-sm",
  
  // Enhanced card with hover effects
  interactive: "rounded-2xl p-6 bg-card shadow-soft hover:shadow-elevated transition-all duration-300 hover:-translate-y-2 border border-border/50 hover:border-primary/20",
  
  // Gradient card backgrounds
  gradientBlue: "bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-6 rounded-2xl border border-blue-200/50 dark:border-blue-700/50 hover:shadow-lg transition-all duration-300 hover:scale-105",
  gradientEmerald: "bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-6 rounded-2xl border border-emerald-200/50 dark:border-emerald-700/50 hover:shadow-lg transition-all duration-300 hover:scale-105",
  gradientPurple: "bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-2xl border border-purple-200/50 dark:border-purple-700/50 hover:shadow-lg transition-all duration-300 hover:scale-105",
  gradientOrange: "bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl border border-orange-200/50 dark:border-orange-700/50 hover:shadow-md transition-all duration-300",
  gradientRed: "bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-xl border border-red-200/50 dark:border-red-700/50 hover:shadow-md transition-all duration-300",
  gradientAmber: "bg-gradient-to-r from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 rounded-xl border border-amber-200/50 dark:border-amber-700/50 hover:shadow-md transition-all duration-300",
  
  // Modal and dialog styles
  modal: "max-w-2xl max-h-[90vh] overflow-y-auto bg-white rounded-2xl shadow-2xl border-0",
  modalCompact: "relative w-full max-w-md bg-white rounded-lg shadow-2xl border-0 max-h-[90vh] overflow-y-auto my-auto",
  
  // Backdrop and glass effects
  glass: "bg-white/95 backdrop-blur-md border border-gray-200/50 shadow-sm",
  glassCard: "bg-background/95 backdrop-blur-sm rounded-2xl shadow-soft border border-primary/10",
  
  // Feature and content cards
  feature: "text-center p-6 rounded-2xl bg-gradient-to-br from-primary/5 to-transparent border border-primary/10 backdrop-blur-sm",
  content: "group flex items-center p-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-xl hover:from-primary/10 hover:to-primary/5 transition-all duration-300 hover:scale-105 hover:shadow-md border border-border/50",
  
  // Stats and metrics cards
  stats: "rounded-2xl p-6 bg-gradient-to-br from-white to-slate-50 shadow-elevated border border-border",
  
  // Search and form cards
  search: "w-full max-w-4xl mx-auto bg-white shadow-xl border-0 rounded-2xl overflow-hidden",
  form: "bg-white rounded-lg shadow-sm border p-4",
  
  // Floating action buttons
  floatingButton: "bg-white/90 backdrop-blur-sm text-gray-800 p-3 rounded-full hover:bg-white hover:scale-110 transition-all duration-300 shadow-lg",
  
  // Badge and tag styles
  badge: "inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary border border-primary/20",
  tag: "inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20"
};

// Helper function to combine card styles with custom classes
export const getCardStyle = (style: keyof typeof cardStyles, customClasses?: string) => {
  return cn(cardStyles[style], customClasses);
};

// Common animation classes for cards
export const cardAnimations = {
  hover: "hover:shadow-elevated transition-all duration-300 hover:-translate-y-2",
  scale: "hover:scale-105 transition-all duration-300",
  glow: "hover:shadow-2xl transition-all duration-300",
  float: "hover:-translate-y-1 transition-all duration-300"
};