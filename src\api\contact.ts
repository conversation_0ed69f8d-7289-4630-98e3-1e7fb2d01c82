import api from "@/lib/axios";

const BASE_ROUTE = "/contact-requests";

export enum ContactReason {
  GeneralBooking = 0,
  DelaysConcerns = 1,
  GroupBooking = 2,
  MediaPress = 3,
  Employment = 4,
  BusinessPartnership = 5,
  Other = 6,
}

interface ContactRequest {
  fullName: string;
  email: string;
  phone: string;
  reason: ContactReason;
  message: string;
}

export const createContactRequestApi = async (request: ContactRequest) => {
  const { data } = await api.post(BASE_ROUTE, request);
  return data;
};

export const getContactRequestsApi = async () => {
  const { data } = await api.get(BASE_ROUTE);
  return data;
};

export const getContactRequestByIdApi = async (id: string) => {
  const { data } = await api.get(`${BASE_ROUTE}/${id}`);
  return data;
};

export const updateContactRequestApi = async (id: string, status: any) => {
  const { data } = await api.patch(`${BASE_ROUTE}/${id}`, { status });
  return data;
};

export const deleteContactRequestApi = async (id: string) => {
  const { data } = await api.delete(`${BASE_ROUTE}/${id}`);
  return data;
};
