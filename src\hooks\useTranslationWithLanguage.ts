import { useLanguage } from "@/providers/LanguageContext";
import { useTranslations } from "next-intl";

/**
 * Custom hook that combines useTranslations and useLanguage
 * to reduce repetitive imports across components
 */
export const useTranslationWithLanguage = () => {
  const t = useTranslations();
  const { isRTL, language, changeLanguage } = useLanguage();

  return {
    t,
    isRTL,
    language,
    changeLanguage,
  };
};

export default useTranslationWithLanguage;
