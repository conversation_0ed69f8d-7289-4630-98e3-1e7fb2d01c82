"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Instagram, Twitter, Linkedin, Facebook } from "lucide-react";
import { useTranslations } from "next-intl";
import { useLanguage } from "@/providers/LanguageContext";
import Link from "next/link";

const Footer = () => {
  const t = useTranslations();
  const { isRTL } = useLanguage();

  return (
    <footer className="bg-[oklch(0.19_0.03_248)] py-16 text-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div
              className={`mb-6 flex items-center gap-4 ${
                isRTL ? "space-x-2 space-x-reverse" : "space-x-2"
              }`}
            >
              <div className="bg-primary flex h-8 w-8 items-center justify-center rounded-lg">
                <span className="text-sm font-bold text-white">H</span>
              </div>
              <span className="text-xl font-semibold text-white">hala</span>
            </div>
            <p className="mb-6 leading-relaxed text-white/80">
              {t("footer.description")}
            </p>
            <div
              className={`flex ${
                isRTL ? "space-x-4 space-x-reverse" : "space-x-4"
              }`}
            >
              <Button
                variant="ghost"
                size="icon"
                className="text-white/80 hover:bg-white/10 hover:text-white"
              >
                <Instagram className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-white/80 hover:bg-white/10 hover:text-white"
              >
                <Twitter className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-white/80 hover:bg-white/10 hover:text-white"
              >
                <Linkedin className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-white/80 hover:bg-white/10 hover:text-white"
              >
                <Facebook className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Company */}
          <div>
            <h3 className="mb-6 text-lg font-semibold text-white">
              {t("footer.company")}
            </h3>
            <ul className="space-y-4">
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.aboutUs")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.careers")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.press")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.blog")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.investorRelations")}
                </a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="mb-6 text-lg font-semibold text-white">
              {t("footer.support")}
            </h3>
            <ul className="space-y-4">
              <li>
                <Link
                  href="/faq"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("faq.title")}
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.helpCenter")}
                </a>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.contactUs")}
                </Link>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.safetySecurity")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.communityGuidelines")}
                </a>
              </li>
              <li>
                <a
                  href="#"
                  className="text-white/80 transition-colors hover:text-white"
                >
                  {t("footer.accessibility")}
                </a>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="mb-6 text-lg font-semibold text-white">
              {t("footer.stayUpdated")}
            </h3>
            <p className="mb-4 text-white/80">
              {t("footer.newsletterDescription")}
            </p>
            <div className="space-y-3">
              <Input
                type="email"
                placeholder={t("footer.emailPlaceholder")}
                className="border-white/20 bg-white/10 text-white placeholder:text-white/60 focus:bg-white/20"
              />
              <Button className="bg-brand-teal hover:bg-brand-teal-dark w-full text-white">
                {t("footer.subscribe")}
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-white/20 pt-8">
          <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
            <p className="text-sm text-white/60">{t("footer.copyright")}</p>
            <div
              className={`flex text-sm ${
                isRTL ? "space-x-6 space-x-reverse" : "space-x-6"
              }`}
            >
              <a
                href="#"
                className="text-white/60 transition-colors hover:text-white"
              >
                {t("footer.privacyPolicy")}
              </a>
              <a
                href="#"
                className="text-white/60 transition-colors hover:text-white"
              >
                {t("footer.termsOfService")}
              </a>
              <a
                href="#"
                className="text-white/60 transition-colors hover:text-white"
              >
                {t("footer.cookiePolicy")}
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
