import { getTranslations } from "next-intl/server";

const ValueProposition: React.FC = async () => {
  const t = await getTranslations("corporate");

  return (
    <section className="bg-muted/50 py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          <div className="space-y-6">
            <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
              {t("valueProposition.title")}
            </h2>
            <p className="text-muted-foreground text-lg leading-relaxed md:text-xl">
              {t("valueProposition.description")}
            </p>
          </div>
          <div className="relative">
            <img
              src="/CorporateStay/corporate_vp.webp"
              alt="Corporate Value Proposition"
              className="h-auto w-full rounded-2xl shadow-2xl transition-transform duration-300 hover:scale-105"
            />
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300 hover:opacity-100"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ValueProposition;
