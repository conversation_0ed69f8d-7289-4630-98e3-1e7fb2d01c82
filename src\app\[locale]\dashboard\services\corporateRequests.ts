import { isAxiosError } from "axios";
import {
  CorporateRequest,
  CorporateRequestStatus,
} from "../types/corporateRequests";
import { PaginationData } from "../types/global";
import api from "@/lib/axios";

export type GetCorporateRequestsParams = {
  pageNumber: number;
  pageSize: number;
  status?: CorporateRequestStatus;
};

export type GetCorporateRequestsResponse = {
  items: CorporateRequest[];
} & PaginationData;

export const getCorporateRequests = async ({
  pageNumber = 1,
  pageSize = 10,
  status,
}: GetCorporateRequestsParams): Promise<GetCorporateRequestsResponse> => {
  try {
    const res = await api<GetCorporateRequestsResponse>(
      "/corporate-pricing-requests",
      {
        params: {
          pageNumber,
          pageSize,
          ...(status !== undefined ? { status } : {}),
        },
      },
    );

    return res.data;
  } catch (error) {
    console.error(error);

    if (isAxiosError(error)) {
      if (error.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (error.response?.status === 403) {
        throw new Error("غير مصرح (403)");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};

export const deleteCorporateRequest = async (id: string) => {
  try {
    const res = await api.delete(`/corporate-pricing-requests/${id}`);
    return res.data;
  } catch (error) {
    console.error(error);

    if (isAxiosError(error)) {
      if (error.response?.status === 400) {
        throw new Error("طلب غير صالح (400)");
      }

      if (error.response?.status === 401) {
        throw new Error("غير مصرح به - مطلوب دور المسؤول (401)");
      }

      if (error.response?.status === 403) {
        throw new Error("(403) ممنوع - أذونات غير كافية");
      }

      if (error.response?.status === 404) {
        throw new Error("(404) غير موجود");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};

export const UpdateCorporateRequestStatus = async (
  id: string,
  status: CorporateRequestStatus,
) => {
  try {
    const res = await api.patch(`/corporate-pricing-requests/${id}`, {
      status: typeof status === "string" ? parseInt(status) : status,
    });

    return res.data;
  } catch (err) {
    console.error(err);

    if (isAxiosError(err)) {
      if (err.response?.status === 400) {
        throw new Error("بيانات الطلب أو الحالة غير صالحة");
      }

      if (err.response?.status === 401) {
        throw new Error("غير مصرح به - مطلوب دور المسؤول (401)");
      }

      if (err.response?.status === 403) {
        throw new Error("ممنوع - أذونات غير كافية");
      }

      if (err.response?.status === 404) {
        throw new Error("غير موجود");
      }
    }

    throw new Error("حدث خطأ غير متوقع");
  }
};
