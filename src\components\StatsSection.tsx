"use client";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { TrendingUp, Users, Heart, Award } from "lucide-react";

type Translate = (key: string) => string;

const getStats = (t: Translate) => [
  {
    value: 50,
    suffix: "K+",
    labelKey: "stats.happyGuests",
    icon: Users,
    color: "from-blue-500 to-blue-600",
    description: "Satisfied customers worldwide",
  },
  {
    value: 200,
    suffix: "+",
    labelKey: "stats.premiumProperties",
    icon: TrendingUp,
    color: "from-green-500 to-green-600",
    description: "Premium accommodations",
  },
  {
    value: 99,
    suffix: "%",
    labelKey: "stats.satisfactionRate",
    icon: Heart,
    color: "from-pink-500 to-pink-600",
    description: "Customer satisfaction rate",
  },
];

const AnimatedCounter = ({
  value,
  suffix,
}: {
  value: number;
  suffix: string;
}) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepValue = value / steps;
    const stepTime = duration / steps;

    let currentStep = 0;
    const timer = setInterval(() => {
      currentStep++;
      setCount(Math.min(Math.round(stepValue * currentStep), value));

      if (currentStep >= steps) {
        clearInterval(timer);
      }
    }, stepTime);

    return () => clearInterval(timer);
  }, [value]);

  return (
    <span className="from-primary to-brand-ocean bg-gradient-to-r bg-clip-text text-4xl font-bold text-transparent md:text-5xl lg:text-6xl">
      {count}
      {suffix}
    </span>
  );
};

const StatsSection = () => {
  const t = useTranslations();
  const stats = getStats(t);

  return (
    <section className="from-background via-brand-warm/20 to-background relative overflow-hidden bg-gradient-to-br py-24">
      {/* Background decorations */}
      <div className="absolute top-0 left-0 h-full w-full opacity-5">
        <div className="absolute top-20 left-10">
          <Award className="text-primary h-24 w-24" />
        </div>
        <div className="absolute right-10 bottom-20">
          <TrendingUp className="text-primary h-20 w-20" />
        </div>
      </div>

      <div className="relative container mx-auto px-4">
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="shadow-elevated border-border rounded-2xl border bg-gradient-to-br from-white to-slate-50 p-6"
            >
              <div className="mb-4 flex items-center gap-4">
                <div
                  className={`h-12 w-12 rounded-xl bg-gradient-to-br ${stat.color} flex items-center justify-center text-white`}
                >
                  <stat.icon className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <p className="text-muted-foreground text-sm">
                    {t(stat.labelKey)}
                  </p>
                  <AnimatedCounter value={stat.value} suffix={stat.suffix} />
                </div>
              </div>
              <p className="text-sm text-slate-500">{stat.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
