import { QuoteIcon } from "lucide-react";
import { getTranslations } from "next-intl/server";

const TrustedBySection: React.FC = async () => {
  const t = await getTranslations("corporate");

  return (
    <section className="bg-muted/40 py-20">
      <div className="relative isolate mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <QuoteIcon className="text-primary/50 absolute -top-[60px] right-0 z-[-1] size-16 md:size-24" />
        <QuoteIcon className="text-primary/50 absolute -bottom-[60px] left-0 z-[-1] size-16 rotate-180 md:size-24" />
        <div className="text-center">
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-4xl">
            {t("trustedBy.title")}
          </h2>
          <p className=" mx-auto max-w-3xl text-lg leading-relaxed md:text-xl">
            {t("trustedBy.description")}
          </p>
        </div>
      </div>
    </section>
  );
};

export default TrustedBySection;
