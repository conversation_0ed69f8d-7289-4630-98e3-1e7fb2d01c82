import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import {
  DollarSign,
  Shield,
  GraduationCap,
  Coffee,
  Car,
  Wifi,
} from "lucide-react";

interface Benefit {
  icon: React.ElementType;
  key: string;
  color: string;
}

const BenefitsSection: React.FC = () => {
  const t = useTranslations("careers.benefits");

  const benefits: Benefit[] = [
    {
      icon: DollarSign,
      key: "salary",
      color: "from-primary to-brand-ocean",
    },
    {
      icon: Shield,
      key: "insurance",
      color: "from-brand-ocean to-primary",
    },
    {
      icon: GraduationCap,
      key: "development",
      color: "from-primary to-brand-teal-dark",
    },
    {
      icon: Coffee,
      key: "environment",
      color: "from-brand-teal-dark to-primary",
    },
    {
      icon: Car,
      key: "transport",
      color: "from-purple-500 to-indigo-500",
    },
    {
      icon: Wifi,
      key: "remote",
      color: "from-indigo-500 to-blue-500",
    },
  ];

  return (
    <section className="from-background to-background/80 bg-gradient-to-br py-20 lg:py-32">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-16 text-center">
          <Badge className="bg-primary/10 text-primary border-primary/20 mb-4">
            {t("badge")}
          </Badge>
          <h2 className="text-foreground mb-6 text-3xl font-bold md:text-5xl">
            {t("title")}
          </h2>
          <p className="text-muted-foreground mx-auto max-w-3xl text-xl">
            {t("subtitle")}
          </p>
        </div>
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {benefits.map((benefit) => (
            <div
              key={benefit.key}
              className="group bg-card border-border relative overflow-hidden rounded-2xl border p-8 shadow-sm transition-all duration-300 hover:shadow-xl"
            >
              <div
                className={`absolute top-0 left-0 h-1 w-full bg-gradient-to-r ${benefit.color} opacity-50 transition-opacity duration-300 group-hover:opacity-100`}
              ></div>
              <div className="relative z-10">
                <div
                  className={`mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br ${benefit.color} text-white shadow-lg transition-transform duration-300 group-hover:scale-110`}
                >
                  <benefit.icon className="h-8 w-8" />
                </div>
                <h3 className="text-foreground mb-3 text-2xl font-bold">
                  {t(`items.${benefit.key}.title`)}
                </h3>
                <p className="text-muted-foreground">
                  {t(`items.${benefit.key}.description`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
