import { getTranslations } from "next-intl/server";
import { AppointmentForm } from "./AppointmentForm";

const CTASection: React.FC = async () => {
  const t = await getTranslations("realEstate.cta");

  return (
    <section className="bg-brand-teal-dark relative overflow-hidden py-20">
      <div className="relative mx-auto max-w-7xl px-4 text-center sm:px-6 lg:px-8">
        <h2 className="animate-fade-in mb-6 text-3xl font-bold text-white md:text-5xl">
          {t("title")}
        </h2>
        <p className="animate-fade-in-delay mx-auto mb-8 max-w-3xl text-lg tracking-wider text-white md:text-xl">
          {t("description")}
        </p>
        <AppointmentForm />
      </div>
    </section>
  );
};

export default CTASection;
