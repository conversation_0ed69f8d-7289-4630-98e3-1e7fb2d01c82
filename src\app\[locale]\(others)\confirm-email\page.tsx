import { Mail } from "lucide-react";
import { Suspense } from "react";
import ConfirmationStatus from "./components/ConfirmationStatus";
import LoadingSpinner from "./components/LoadingSpinner";
import { useTranslations } from "next-intl";

function ConfirmEmailContent() {
  const t = useTranslations();

  return (
    <div className="relative flex min-h-screen items-center justify-center overflow-hidden">
      {/* Enhanced Background */}
      <div className="from-primary/5 via-background to-primary/10 absolute inset-0 bg-gradient-to-br" />
      <div className="from-primary/10 absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] via-transparent to-transparent" />

      {/* Floating Elements */}
      <div className="bg-primary/5 absolute top-20 left-10 h-32 w-32 rounded-full blur-3xl" />
      <div className="bg-primary/5 absolute right-10 bottom-20 h-40 w-40 rounded-full blur-3xl" />
      <div className="bg-primary/3 absolute top-1/2 left-1/4 h-24 w-24 rounded-full blur-2xl" />
      <div className="bg-primary/3 absolute right-1/4 bottom-1/3 h-36 w-36 rounded-full blur-2xl" />

      <div className="relative z-10 w-full max-w-md px-4">
        <div className="mb-8 text-center">
          <div className="bg-primary/10 mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">
            <Mail className="text-primary h-6 w-6" />
          </div>
          <h1 className="mb-2 text-2xl font-bold">
            {t("auth.emailConfirmationTitle") || "Verify Your Email Address"}
          </h1>
          <p className="text-muted-foreground text-sm">
            {t("auth.emailConfirmationDescription") ||
              "We're verifying your email address to complete your account setup."}
          </p>
        </div>

        <Suspense fallback={<LoadingSpinner />}>
          <ConfirmationStatus />
        </Suspense>
      </div>
    </div>
  );
}

export default function ConfirmEmailPage() {
  return <ConfirmEmailContent />;
}
