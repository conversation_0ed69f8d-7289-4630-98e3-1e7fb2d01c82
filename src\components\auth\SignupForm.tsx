import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import useRegister from "@/hooks/auth/useRegister";
import { createAuthValidations } from "@/lib/validations/auth";
import { useAuthDialogStore } from "@/store/authDialogStore";
import { zodResolver } from "@hookform/resolvers/zod";
import { isAxiosError } from "axios";
import { MailIcon, Phone } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import FormFeedback from "../FormFeedback";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import EmailInput from "../ui/email-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Label } from "../ui/label";
import PasswordInput from "../ui/password-input";
import SigninWithGoogleButton from "./SigninWithGoogleButton";

export default function SignupForm() {
  const t = useTranslations();
  const locale = useLocale();
  const isRTL = locale === "ar";

  const { signupSchema } = createAuthValidations(t);
  const closeAuthDialog = useAuthDialogStore((state) => state.closeDialog);

  const form = useForm<z.infer<typeof signupSchema>>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
    },
  });

  const [isTermsChecked, setIsTermsChecked] = useState(false);

  const {
    mutate: register,
    error,
    isPending,
    isSuccess,
    reset: resetMutation,
  } = useRegister();

  const onSubmit = (values: z.infer<typeof signupSchema>) => {
    try {
      register(
        {
          FirstName: values.firstName,
          LastName: values.lastName,
          PhoneNumber: values.phoneNumber,
          Email: values.email,
          Password: values.password,
        },
        {
          onSuccess: () => {
            form.reset();
            setIsTermsChecked(false);
          },
        },
      );

      if (error && isAxiosError(error)) {
        if (error.response?.data.status === 409) {
          form.setError("root", {
            message: t("register.conflictError"),
          });
        } else if (error.response?.data.status === 500) {
          form.setError("root", {
            message: t("errors.serverError"),
          });
        } else {
          form.setError("root", {
            message: t("errors.generalError"),
          });
        }
      }
    } catch (error) {
      form.setError("root", {
        message: t("errors.generalError"),
      });

      console.error(error);
    }
  };

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          noValidate
          className="space-y-4"
        >
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel>{t("auth.firstName")}</FormLabel>
                  <FormControl>
                    <Input placeholder={t("auth.enterFirstName")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel>{t("auth.lastName")}</FormLabel>
                  <FormControl>
                    <Input placeholder={t("auth.enterLastName")} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel>{t("auth.phoneNumber")}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Phone className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                    <Input
                      type="tel"
                      placeholder={t("auth.enterPhoneNumber")}
                      className="pl-10"
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel>{t("auth.email")}</FormLabel>
                <FormControl>
                  <EmailInput placeholder={t("auth.enterEmail")} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel>{t("auth.password")}</FormLabel>
                <FormControl>
                  <PasswordInput {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel>{t("auth.confirmPassword")}</FormLabel>
                <FormControl>
                  <PasswordInput {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {form.formState.errors.root?.message && (
            <Alert variant="destructive" className="bg-red-500/40 text-red-600">
              <AlertTitle>{isRTL ? "خطأ" : "Error"}</AlertTitle>
              <AlertDescription>
                {form.formState.errors.root?.message}
              </AlertDescription>
            </Alert>
          )}

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={isTermsChecked}
              onChange={(e) => setIsTermsChecked(e.target.checked)}
              id="modal-terms"
              className="rounded border-gray-300 accent-[#279fc7]"
            />
            <Label htmlFor="modal-terms" className="text-sm">
              {t("auth.agreeToTerms")}{" "}
              <Button
                type="button"
                variant="link"
                className="h-auto p-0 text-sm text-[#279fc7] hover:text-[#279fc7]/90"
              >
                {t("auth.termsOfService")}
              </Button>{" "}
              {t("auth.and")}{" "}
              <Button
                type="button"
                variant="link"
                className="h-auto p-0 text-sm text-[#279fc7] hover:text-[#279fc7]/90"
              >
                {t("auth.privacyPolicy")}
              </Button>
            </Label>
          </div>

          {form.formState.isSubmitted && !isTermsChecked && (
            <p className="text-sm text-red-500">
              {isRTL
                ? "يجب الموافقة على الشروط والأحكام"
                : "You must agree to the terms and conditions"}
            </p>
          )}

          <Button
            type="submit"
            className="w-full bg-[#279fc7] text-white hover:bg-[#279fc7]/90"
            disabled={isPending || !isTermsChecked}
          >
            {isPending ? t("auth.creatingAccount") : t("auth.createAccount")}
          </Button>

          <div className="space-y-3">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2">
                  {isRTL ? "أو" : "Or"}
                </span>
              </div>
            </div>

            <SigninWithGoogleButton />
          </div>
        </form>
      </Form>

      <FormFeedback
        open={isSuccess}
        onOpenChange={(open) => {
          if (!open) {
            resetMutation();
            closeAuthDialog();
          }
        }}
        title={t("auth.signup.success.title")}
        description={t("auth.signup.success.description")}
        icon={MailIcon}
      />
    </>
  );
}
