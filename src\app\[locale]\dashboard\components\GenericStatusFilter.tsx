"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { DefaultStatus } from "../types/global";

export const defaultStatusLabels: Record<DefaultStatus, string> = {
  [DefaultStatus.NotApproved]: "غير معتمد",
  [DefaultStatus.Pending]: "معلق",
  [DefaultStatus.Approved]: "معتمد",
  [DefaultStatus.Rejected]: "مرفوض",
};

export const defaultStatusClasses: Record<DefaultStatus, string> = {
  [DefaultStatus.NotApproved]:
    "bg-gray-100 text-gray-800 hover:!bg-gray-200 hover:!text-gray-800",
  [DefaultStatus.Pending]:
    "bg-yellow-100 text-yellow-800 hover:!bg-yellow-200 hover:!text-gray-800",
  [DefaultStatus.Approved]:
    "bg-green-100 text-green-800 hover:!bg-green-200 hover:!text-gray-800",
  [DefaultStatus.Rejected]:
    "bg-red-100 text-red-800 hover:!bg-red-200 hover:!text-gray-800",
};

export interface StatusOption<T extends string | number> {
  value: T;
  label: string;
  className: string;
}

interface GenericStatusFilterProps<T extends string | number> {
  statusParamName?: string;
  options?: StatusOption<T>[];
  defaultLabel?: string;
  filterLabel?: string;
}

function getDefaultOptions(): StatusOption<DefaultStatus>[] {
  return Object.entries(defaultStatusLabels).map(([key, value]) => ({
    value: key as unknown as DefaultStatus,
    label: value,
    className: defaultStatusClasses[key as unknown as DefaultStatus],
  }));
}

export default function GenericStatusFilter<T extends string | number>({
  statusParamName = "status",
  options,
  defaultLabel = "اختر الحالة",
  filterLabel = "الحالة:",
}: GenericStatusFilterProps<T>) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const statusParam = searchParams.get(statusParamName);
  const statusValue = statusParam !== null ? statusParam : "all";

  return (
    <div className="me-auto flex items-center gap-2">
      <span className="text-sm">{filterLabel}</span>
      <Select
        dir="rtl"
        value={statusValue}
        onValueChange={(v) => {
          const params = new URLSearchParams(searchParams.toString());

          if (v === "all") {
            params.delete(statusParamName);
          } else {
            params.set(statusParamName, v);
          }

          // reset to first page when filter changes
          params.set("pageNumber", "1");

          router.push(`${pathname}?${params.toString()}`, {
            scroll: false,
          });
        }}
      >
        <SelectTrigger>
          <SelectValue placeholder={defaultLabel} />
        </SelectTrigger>
        <SelectContent align="start">
          <SelectItem value="all">الكل</SelectItem>
          {(options || getDefaultOptions()).map((option) => (
            <SelectItem
              className={cn("mt-1 hover:!bg-transparent", option.className)}
              key={String(option.value)}
              value={String(option.value)}
            >
              <span>{option.label}</span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
