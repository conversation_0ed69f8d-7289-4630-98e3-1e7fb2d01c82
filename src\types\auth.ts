export interface LoginResponse {
  email: string;
  expiresIn: number;
  firstName: string;
  id: string;
  lastName: string;
  refreshToken: string;
  refreshTokenExpiration: Date;
  token: string;
}

export interface RegisterRequest {
  FirstName: string;
  LastName: string;
  PhoneNumber: string;
  Email: string;
  Password: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

export interface ResetPasswordRequest {
  email: string;
  token: string;
  newPassword: string;
}
