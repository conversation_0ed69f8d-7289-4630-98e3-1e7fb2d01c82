import api from "@/lib/axios";

const BASE_ROUTE = "/investor-requests";

interface InvestorRequest {
  fullName: string;
  email: string;
  phone: string;
  propertyLocation: string;
  message: string;
}

export const createInvestorRequestApi = async (request: InvestorRequest) => {
  const { data } = await api.post(BASE_ROUTE, request);
  return data;
};

export const getInvestorRequestsApi = async () => {
  const { data } = await api.get(BASE_ROUTE);
  return data;
};

export const getInvestorRequestByIdApi = async (id: string) => {
  const { data } = await api.get(`${BASE_ROUTE}/${id}`);
  return data;
};

export const updateInvestorRequestStatusApi = async (
  id: string,
  status: "approved" | "rejected",
) => {
  const { data } = await api.patch(`${BASE_ROUTE}/${id}`, { status });
  return data;
};

export const deleteInvestorRequestApi = async (id: string) => {
  const { data } = await api.delete(`${BASE_ROUTE}/${id}`);
  return data;
};
