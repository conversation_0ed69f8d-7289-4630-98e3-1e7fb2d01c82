export type PaginationData = {
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
};

export enum DefaultStatus {
  NotApproved = 0,
  Pending = 1,
  Approved = 2,
  Rejected = 3,
}

export const defaultStatusLabels: Record<DefaultStatus, string> = {
  [DefaultStatus.NotApproved]: "غير معتمد",
  [DefaultStatus.Pending]: "معلق",
  [DefaultStatus.Approved]: "معتمد",
  [DefaultStatus.Rejected]: "مرفوض",
};

export const defaultStatusClasses: Record<DefaultStatus, string> = {
  [DefaultStatus.NotApproved]: "bg-gray-100 text-gray-800 hover:!bg-gray-200 hover:!text-gray-800",
  [DefaultStatus.Pending]: "bg-yellow-100 text-yellow-800 hover:!bg-yellow-200 hover:!text-gray-800",
  [DefaultStatus.Approved]: "bg-green-100 text-green-800 hover:!bg-green-200 hover:!text-gray-800",
  [DefaultStatus.Rejected]: "bg-red-100 text-red-800 hover:!bg-red-200 hover:!text-gray-800",
};
