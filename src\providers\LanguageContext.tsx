"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { Locale, useLocale } from "next-intl";
import { usePathname, useRouter } from "next/navigation";

interface LanguageContextType {
  language: Locale;
  isRTL: boolean;
  changeLanguage: (lang: Locale) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined,
);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};

interface LanguageProviderProps {
  children: React.ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({
  children,
}) => {
  const locale = useLocale() as Locale; // ⬅️ نوع مضبوط
  const router = useRouter();
  const pathname = usePathname();

  const [language, setLanguage] = useState<Locale>(locale);
  const [isRTL, setIsRTL] = useState(locale === "ar");

  const changeLanguage = (lang: Locale) => {
    const segments = pathname.split("/");
    segments[1] = lang; // أول segment هو كود اللغة
    const newPath = segments.join("/");

    router.push(newPath);
    setLanguage(lang);
    setIsRTL(lang === "ar");

    document.documentElement.dir = lang === "ar" ? "rtl" : "ltr";
    document.documentElement.lang = lang;
  };

  useEffect(() => {
    document.documentElement.dir = isRTL ? "rtl" : "ltr";
    document.documentElement.lang = language;
  }, [isRTL, language]);

  const value: LanguageContextType = {
    language,
    isRTL,
    changeLanguage,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
