import { create } from "zustand";

export type AuthType = "signin" | "signup";

interface AuthDialogStore {
  isOpen: boolean;
  authType: AuthType;
  openDialog: (type?: AuthType) => void;
  closeDialog: () => void;
  setAuthType: (t: AuthType) => void;
  setMessage: (
    message: { type: "success" | "error"; text: string } | null,
  ) => void;
  message: { type: "success" | "error"; text: string } | null;
}

export const useAuthDialogStore = create<AuthDialogStore>((set) => ({
  isOpen: false,
  authType: "signin",
  openDialog: (type = "signin") => set({ isOpen: true, authType: type }),
  closeDialog: () => set({ isOpen: false }),
  setAuthType: (type: AuthType) =>
    set(() => ({
      authType: type,
    })),
  setMessage: (message: { type: "success" | "error"; text: string } | null) =>
    set(() => ({
      message,
    })),
  message: null,
}));
