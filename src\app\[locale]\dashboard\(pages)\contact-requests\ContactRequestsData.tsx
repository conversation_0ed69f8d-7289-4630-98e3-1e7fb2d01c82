"use client";

import GenericPageLoading from "@/components/GenericPageLoading";
import ApiError from "../../components/ApiError";
import { DataTable } from "../../components/data-table";
import GenericStatusFilter from "../../components/GenericStatusFilter";
import useGetContactRequests from "../../hooks/contactRequests/useGetContactRequests";
import contactRequestsColumns from "./ContactRequestsColumns";

export default function ContactRequestsData() {
  const { data, isLoading, isError, error } = useGetContactRequests();

  if (isLoading) {
    return <GenericPageLoading />;
  }

  if (isError || !data) {
    return <ApiError error={error as Error} />;
  }

  return (
    <div>
      <DataTable
        columns={contactRequestsColumns}
        data={data.items}
        pagination={{
          pageNumber: data.pageNumber,
          totalPages: data.totalPages,
          hasPreviousPage: data.hasPreviousPage,
          hasNextPage: data.hasNextPage,
        }}
        beforeTable={<GenericStatusFilter />}
      />
    </div>
  );
}
